import { NextRequest, NextResponse } from 'next/server';
import { studyTools } from '@/lib/genkit/study-flows';

export async function POST(request: NextRequest) {
  try {
    const { tool, params } = await request.json();

    if (!tool) {
      return NextResponse.json({ 
        error: 'Tool name is required' 
      }, { status: 400 });
    }

    let result;

    switch (tool) {
      case 'explainConcept':
        result = await studyTools.explainConcept(params);
        break;
      
      case 'createQuiz':
        result = await studyTools.createQuiz(params);
        break;
      
      case 'summarizeContent':
        result = await studyTools.summarizeContent(params);
        break;
      
      default:
        return NextResponse.json({ 
          error: `Unknown tool: ${tool}` 
        }, { status: 400 });
    }

    return NextResponse.json({
      result,
      tool,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Tools API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}