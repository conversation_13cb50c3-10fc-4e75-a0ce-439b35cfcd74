import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    const whereClause = category ? { category } : {};

    const topics = await db.topic.findMany({
      where: whereClause,
      orderBy: { name: 'asc' }
    });

    return NextResponse.json({ topics });

  } catch (error) {
    console.error('Get topics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, category, difficulty } = await request.json();

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Check if topic already exists
    const existingTopic = await db.topic.findUnique({
      where: { name }
    });

    if (existingTopic) {
      return NextResponse.json({ error: 'Topic already exists' }, { status: 400 });
    }

    const topic = await db.topic.create({
      data: {
        name,
        description,
        category,
        difficulty
      }
    });

    return NextResponse.json({ topic });

  } catch (error) {
    console.error('Create topic error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}