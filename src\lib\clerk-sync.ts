import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";

export async function getOrCreateUser() {
  const { userId } = await auth();
  
  if (!userId) {
    return null;
  }

  try {
    // First, try to find the user by clerkId
    let user = await db.user.findUnique({
      where: { clerkId: userId }
    });

    if (!user) {
      // If user doesn't exist, get user info from Clerk
      const clerkUser = await currentUser();
      
      if (!clerkUser) {
        return null;
      }

      // Create new user in our database
      user = await db.user.create({
        data: {
          clerkId: userId,
          email: clerkUser.emailAddresses[0]?.emailAddress || "",
          name: clerkUser.fullName || "",
          firstName: clerkUser.firstName || "",
          lastName: clerkUser.lastName || "",
          imageUrl: clerkUser.imageUrl || "",
        }
      });
    }

    return user;
  } catch (error) {
    console.error("Error in getOrCreateUser:", error);
    return null;
  }
}

export async function requireAuth() {
  const user = await getOrCreateUser();
  
  if (!user) {
    throw new Error("Authentication required");
  }
  
  return user;
}