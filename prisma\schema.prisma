// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id              String   @id @default(cuid())
  clerkId         String   @unique
  email           String   @unique
  name            String?
  firstName       String?
  lastName        String?
  imageUrl        String?
  bio             String?
  studyLevel      String?  // 'high-school', 'undergraduate', 'graduate', 'professional', 'other'
  preferredSubjects String? // JSON array
  learningGoal    String?
  aiResponseStyle String   @default("balanced") // 'concise', 'balanced', 'comprehensive', 'conversational'
  notificationsEnabled Boolean @default(true)
  studyReminders  Boolean  @default(true)
  darkMode        Boolean  @default(false)
  autonomousMode  Boolean  @default(true)
  adaptiveDifficulty Boolean @default(true)
  progressTracking Boolean @default(true)
  preferredSessionDuration Int @default(30) // in minutes
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  studySessions   StudySession[]
  conversations   Conversation[]
  studyProgress   StudyProgress[]
  studyGoals      StudyGoal[]
  learningSessions LearningSession[]
}

model StudySession {
  id          String   @id @default(cuid())
  title       String
  description String?
  userId      String
  topicId     String?
  startTime   DateTime @default(now())
  endTime     DateTime?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  topic        Topic?         @relation(fields: [topicId], references: [id])
  conversation Conversation[]
  learningSessions LearningSession[]
}

model Conversation {
  id             String   @id @default(cuid())
  sessionId      String
  userId         String
  title          String?
  systemPrompt   String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  session      StudySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages     Message[]
}

model Message {
  id           String   @id @default(cuid())
  conversationId String
  role         String   // 'user' or 'assistant'
  content      String
  metadata     String?  // JSON string for additional data
  createdAt    DateTime @default(now())

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
}

model Topic {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  category    String?
  difficulty  Int?     // 1-5 scale
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  studySessions StudySession[]
  studyProgress StudyProgress[]
}

model StudyProgress {
  id          String   @id @default(cuid())
  userId      String
  topicId     String
  level       Int      @default(1) // 1-10 proficiency level
  xp          Int      @default(0)  // Experience points
  timeSpent   Int      @default(0)  // Minutes spent studying
  lastStudied DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  topic Topic @relation(fields: [topicId], references: [id], onDelete: Cascade)

  @@unique([userId, topicId])
}

model StudyGoal {
  id          String   @id @default(cuid())
  userId      String
  title       String
  description String?
  targetDate  DateTime?
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Curriculum {
  id                  String   @id @default(cuid())
  topic               String
  subject             String
  description         String
  difficulty          String   // 'beginner', 'intermediate', 'advanced'
  estimatedTotalTime  String
  learningObjectives  String   // JSON array
  prerequisites       String   // JSON array
  steps               String   // JSON array of steps
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  learningSessions LearningSession[]
}

model LearningSession {
  id                 String   @id @default(cuid())
  userId             String
  studySessionId     String?
  curriculumId       String?
  currentPhase       String   // 'planning', 'explanation', 'qa', 'practice', 'assessment', 'completion'
  currentStepIndex   Int      @default(0)
  progress           Float    @default(0) // 0-100
  startTime          DateTime @default(now())
  endTime            DateTime?
  isCompleted        Boolean  @default(false)
  adaptiveDifficulty String   @default("intermediate")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  studySession    StudySession?      @relation(fields: [studySessionId], references: [id])
  curriculum      Curriculum?         @relation(fields: [curriculumId], references: [id])
  userResponses   UserResponse[]
  userInteractions UserInteraction[]
}

model UserResponse {
  id          String   @id @default(cuid())
  sessionId   String
  stepId      String
  response    String
  responseType String   // 'proceed', 'question', 'clarification', 'skip', 'repeat', 'help'
  wasHelpful  Boolean?
  timestamp   DateTime @default(now())

  // Relations
  session LearningSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
}

model UserInteraction {
  id          String   @id @default(cuid())
  sessionId   String
  message     String
  responseType String   // 'proceed', 'question', 'clarification', 'skip', 'repeat', 'help'
  targetStepId String?
  timestamp   DateTime @default(now())

  // Relations
  session LearningSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
}