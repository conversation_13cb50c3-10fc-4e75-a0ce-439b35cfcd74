import { NextRequest, NextResponse } from 'next/server';
import { runStudyAssistant } from '@/lib/genkit/study-flows';

export async function GET() {
  try {
    console.log('Testing Genkit integration...');
    
    // Test the study assistant flow
    const response = await runStudyAssistant({
      message: "Explain photosynthesis in simple terms",
      conversationHistory: [],
      sessionId: 'test-session',
      systemPrompt: "You are a helpful AI study assistant.",
    });

    console.log('Genkit response:', response);

    return NextResponse.json({
      success: true,
      response: response.response,
      suggestedActions: response.suggestedActions,
      studyTips: response.studyTips,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Genkit test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}