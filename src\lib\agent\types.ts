import { z } from 'zod';

// Agent Phase Types
export const AgentPhase = z.enum([
  'planning',        // Analyze user needs and create learning plan
  'assessment',      // Evaluate current knowledge level
  'explanation',     // Teach concepts and explain topics
  'practice',        // Provide exercises and practice problems
  'qa',             // Answer questions and clarify doubts
  'feedback',       // Provide feedback on performance
  'adaptation',     // Adjust difficulty and approach based on performance
  'reflection',     // Review progress and consolidate learning
  'completion'      // Summarize learning and provide next steps
]);

export type AgentPhaseType = z.infer<typeof AgentPhase>;

// Agent State Schema
export const AgentStateSchema = z.object({
  sessionId: z.string(),
  currentPhase: AgentPhase,
  previousPhase: AgentPhase.optional(),
  phaseHistory: z.array(AgentPhase),
  learningGoals: z.array(z.string()),
  currentTopic: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  userPerformance: z.object({
    correctAnswers: z.number().default(0),
    totalQuestions: z.number().default(0),
    understandingLevel: z.number().min(0).max(1).default(0.5),
    engagementScore: z.number().min(0).max(1).default(0.5),
    lastResponseTime: z.number().optional(),
  }),
  context: z.object({
    conversationHistory: z.array(z.object({
      role: z.enum(['user', 'assistant']),
      content: z.string(),
      timestamp: z.date(),
    })),
    currentStep: z.number().default(0),
    totalSteps: z.number().default(0),
    learningObjectives: z.array(z.string()),
    prerequisites: z.array(z.string()),
  }),
  metadata: z.object({
    startTime: z.date(),
    lastActivity: z.date(),
    phaseTransitions: z.number().default(0),
    adaptations: z.number().default(0),
  }),
});

export type AgentState = z.infer<typeof AgentStateSchema>;

// Phase Transition Schema
export const PhaseTransitionSchema = z.object({
  fromPhase: AgentPhase,
  toPhase: AgentPhase,
  reason: z.string(),
  confidence: z.number().min(0).max(1),
  triggers: z.array(z.string()),
  timestamp: z.date(),
});

export type PhaseTransition = z.infer<typeof PhaseTransitionSchema>;

// Decision Criteria Schema
export const DecisionCriteriaSchema = z.object({
  userUnderstanding: z.number().min(0).max(1),
  userEngagement: z.number().min(0).max(1),
  topicComplexity: z.number().min(0).max(1),
  timeSpent: z.number(),
  questionFrequency: z.number(),
  performanceTrend: z.enum(['improving', 'stable', 'declining']),
  userFeedback: z.array(z.string()),
});

export type DecisionCriteria = z.infer<typeof DecisionCriteriaSchema>;

// Agent Action Schema
export const AgentActionSchema = z.object({
  type: z.enum(['message', 'quiz', 'explanation', 'practice', 'feedback', 'adaptation']),
  content: z.string(),
  metadata: z.record(z.unknown()).optional(),
  expectedResponse: z.string().optional(),
  confidence: z.number().min(0).max(1).default(0.8),
});

export type AgentAction = z.infer<typeof AgentActionSchema>;

// Learning Plan Schema
export const LearningPlanSchema = z.object({
  id: z.string(),
  sessionId: z.string(),
  topic: z.string(),
  objectives: z.array(z.string()),
  phases: z.array(z.object({
    phase: AgentPhase,
    description: z.string(),
    estimatedDuration: z.number(), // in minutes
    learningGoals: z.array(z.string()),
    successCriteria: z.array(z.string()),
  })),
  adaptivePath: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type LearningPlan = z.infer<typeof LearningPlanSchema>;

// Explanation Step Schema
export const ExplanationStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  priority: z.number(),
  estimatedTime: z.number(), // in minutes
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  interactive: z.boolean(),
  examples: z.array(z.string()),
  checkpoints: z.array(z.string()),
});

export type ExplanationStep = z.infer<typeof ExplanationStepSchema>;