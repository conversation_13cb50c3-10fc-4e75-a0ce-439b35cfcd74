"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>, BookO<PERSON>, Setting<PERSON> } from "lucide-react";

export function AuthNav() {
  return (
    <nav className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-6">
          <Link href="/" className="flex items-center space-x-2">
            <Brain className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold text-foreground">AI Study Platform</span>
          </Link>
          
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/app" className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors">
              <BookOpen className="h-4 w-4" />
              <span>Study Hub</span>
            </Link>
            <Link href="/app/ai-agent" className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors">
              <Brain className="h-4 w-4" />
              <span>AI Agent</span>
            </Link>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <Link href="/settings">
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </Link>
          
          <UserButton 
            afterSignOutUrl="/"
            appearance={{
              elements: {
                userButtonAvatarBox: "h-8 w-8",
                userButtonPopoverCard: "bg-card border-border",
                userButtonPopoverActionButton: "text-foreground hover:bg-muted",
                userButtonPopoverActionButtonText: "text-foreground",
                userButtonPopoverActionButtonIcon: "text-muted-foreground",
              },
            }}
          />
        </div>
      </div>
    </nav>
  );
}