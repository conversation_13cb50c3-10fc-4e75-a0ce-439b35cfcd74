'use client';

import React, { useState, useEffect } from 'react';
import { useAutonomousAgent } from '@/hooks/use-autonomous-agent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, Clock, Target, TrendingUp, MessageSquare, BookOpen, Trophy } from 'lucide-react';

interface AutonomousLearningAgentProps {
  sessionId: string;
  onSessionEnd?: () => void;
}

export function AutonomousLearningAgent({ sessionId, onSessionEnd }: AutonomousLearningAgentProps) {
  const [topic, setTopic] = useState('');
  const [goals, setGoals] = useState<string[]>([]);
  const [newGoal, setNewGoal] = useState('');
  const [message, setMessage] = useState('');
  const [progressData, setProgressData] = useState<any>(null);

  const {
    state,
    isLoading,
    error,
    initializeAgent,
    sendMessage,
    reportActionResult,
    getProgress,
    endSession,
    currentAction,
    phaseTransition,
  } = useAutonomousAgent(sessionId);

  const handleInitialize = async () => {
    if (!topic.trim()) return;
    await initializeAgent(topic.trim(), goals);
  };

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    await sendMessage(message.trim());
    setMessage('');
  };

  const handleAddGoal = () => {
    if (newGoal.trim() && !goals.includes(newGoal.trim())) {
      setGoals([...goals, newGoal.trim()]);
      setNewGoal('');
    }
  };

  const handleRemoveGoal = (goalToRemove: string) => {
    setGoals(goals.filter(goal => goal !== goalToRemove));
  };

  const handleGetProgress = async () => {
    const progress = await getProgress();
    setProgressData(progress);
  };

  const handleEndSession = async () => {
    await endSession();
    onSessionEnd?.();
  };

  const getPhaseColor = (phase: string) => {
    const colors: Record<string, string> = {
      planning: 'bg-blue-100 text-blue-800',
      assessment: 'bg-purple-100 text-purple-800',
      explanation: 'bg-green-100 text-green-800',
      practice: 'bg-orange-100 text-orange-800',
      qa: 'bg-yellow-100 text-yellow-800',
      feedback: 'bg-pink-100 text-pink-800',
      adaptation: 'bg-indigo-100 text-indigo-800',
      reflection: 'bg-teal-100 text-teal-800',
      completion: 'bg-gray-100 text-gray-800',
    };
    return colors[phase] || 'bg-gray-100 text-gray-800';
  };

  const getPhaseIcon = (phase: string) => {
    const icons: Record<string, React.ReactNode> = {
      planning: <Target className="w-4 h-4" />,
      assessment: <Brain className="w-4 h-4" />,
      explanation: <BookOpen className="w-4 h-4" />,
      practice: <TrendingUp className="w-4 h-4" />,
      qa: <MessageSquare className="w-4 h-4" />,
      feedback: <Trophy className="w-4 h-4" />,
      adaptation: <Brain className="w-4 h-4" />,
      reflection: <Clock className="w-4 h-4" />,
      completion: <Trophy className="w-4 h-4" />,
    };
    return icons[phase] || <Brain className="w-4 h-4" />;
  };

  const renderActionContent = (action: any) => {
    if (!action) return null;

    switch (action.type) {
      case 'quiz':
        return (
          <Card className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Quiz Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{action.content}</p>
              <div className="flex gap-2">
                <Button 
                  onClick={() => reportActionResult('quiz', { 
                    answer: 'correct', 
                    confidence: 0.8 
                  })}
                >
                  Submit Answer
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'explanation':
        return (
          <Card className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Explanation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{action.content}</p>
              <div className="flex gap-2">
                <Button 
                  onClick={() => reportActionResult('explanation', { 
                    understood: true, 
                    confidence: 0.7 
                  })}
                >
                  I Understand
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => reportActionResult('explanation', { 
                    understood: false, 
                    needClarification: true 
                  })}
                >
                  Need Clarification
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'practice':
        return (
          <Card className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Practice Exercise
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{action.content}</p>
              <div className="flex gap-2">
                <Button 
                  onClick={() => reportActionResult('practice', { 
                    completed: true, 
                    difficulty: action.metadata?.difficulty 
                  })}
                >
                  Complete Exercise
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => reportActionResult('practice', { 
                    needHelp: true 
                  })}
                >
                  Need Help
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'feedback':
        return (
          <Card className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="w-5 h-5" />
                Feedback
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{action.content}</p>
              <div className="flex gap-2">
                <Button 
                  onClick={() => reportActionResult('feedback', { 
                    acknowledged: true 
                  })}
                >
                  Got It
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return (
          <Card className="mb-4">
            <CardContent className="pt-6">
              <p>{action.content}</p>
            </CardContent>
          </Card>
        );
    }
  };

  if (!state) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Start Autonomous Learning Session</CardTitle>
            <CardDescription>
              Initialize your AI-powered learning agent by providing a topic and goals
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                What would you like to learn?
              </label>
              <Input
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="e.g., Machine Learning, Spanish Grammar, Calculus..."
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Learning Goals (optional)
              </label>
              <div className="space-y-2">
                {goals.map((goal, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Badge variant="secondary">{goal}</Badge>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveGoal(goal)}
                    >
                      ×
                    </Button>
                  </div>
                ))}
                <div className="flex gap-2">
                  <Input
                    value={newGoal}
                    onChange={(e) => setNewGoal(e.target.value)}
                    placeholder="Add a learning goal..."
                    onKeyPress={(e) => e.key === 'Enter' && handleAddGoal()}
                  />
                  <Button onClick={handleAddGoal}>Add</Button>
                </div>
              </div>
            </div>

            <Button 
              onClick={handleInitialize}
              disabled={!topic.trim() || isLoading}
              className="w-full"
            >
              {isLoading ? 'Initializing...' : 'Start Learning Session'}
            </Button>

            {error && (
              <div className="text-red-600 bg-red-50 p-3 rounded-md">
                {error}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Learning: {state.currentTopic}</h1>
          <p className="text-gray-600">Autonomous AI Learning Session</p>
        </div>
        <Button onClick={handleEndSession} variant="outline">
          End Session
        </Button>
      </div>

      {/* Current Phase */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getPhaseIcon(state.currentPhase)}
            Current Phase: {state.currentPhase.charAt(0).toUpperCase() + state.currentPhase.slice(1)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Badge className={getPhaseColor(state.currentPhase)}>
              {state.currentPhase}
            </Badge>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              {Math.floor((Date.now() - new Date(state.metadata.startTime).getTime()) / 60000)} minutes
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Target className="w-4 h-4" />
              {state.metadata.phaseTransitions} phase transitions
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Phase Transition Notification */}
      {phaseTransition && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-800">
                Phase Transition: {phaseTransition.fromPhase} → {phaseTransition.toPhase}
              </span>
            </div>
            <p className="text-blue-700 mt-2">{phaseTransition.reason}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-4">
          {/* Current Action */}
          {currentAction && renderActionContent(currentAction)}

          {/* Conversation */}
          <Card>
            <CardHeader>
              <CardTitle>Conversation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {state.context.conversationHistory.map((msg, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      msg.role === 'user'
                        ? 'bg-blue-100 ml-8'
                        : 'bg-gray-100 mr-8'
                    }`}
                  >
                    <div className="font-medium text-sm mb-1">
                      {msg.role === 'user' ? 'You' : 'AI Assistant'}
                    </div>
                    <div className="text-sm">{msg.content}</div>
                  </div>
                ))}
              </div>

              <div className="mt-4 flex gap-2">
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                />
                <Button onClick={handleSendMessage} disabled={!message.trim() || isLoading}>
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Understanding</span>
                  <span>{Math.round(state.userPerformance.understandingLevel * 100)}%</span>
                </div>
                <Progress value={state.userPerformance.understandingLevel * 100} />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Engagement</span>
                  <span>{Math.round(state.userPerformance.engagementScore * 100)}%</span>
                </div>
                <Progress value={state.userPerformance.engagementScore * 100} />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Accuracy</span>
                  <span>
                    {state.userPerformance.totalQuestions > 0
                      ? Math.round((state.userPerformance.correctAnswers / state.userPerformance.totalQuestions) * 100)
                      : 0}%
                  </span>
                </div>
                <Progress 
                  value={state.userPerformance.totalQuestions > 0 
                    ? (state.userPerformance.correctAnswers / state.userPerformance.totalQuestions) * 100 
                    : 0} 
                />
              </div>

              <div className="text-sm text-gray-600">
                <div>Questions: {state.userPerformance.correctAnswers}/{state.userPerformance.totalQuestions}</div>
                <div>Difficulty: {state.difficulty}</div>
              </div>
            </CardContent>
          </Card>

          {/* Phase History */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Phase History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {state.phaseHistory.map((phase, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {getPhaseIcon(phase)}
                    <Badge className={getPhaseColor(phase)}>
                      {phase}
                    </Badge>
                    {index === state.phaseHistory.length - 1 && (
                      <span className="text-xs text-gray-500">(current)</span>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <Button onClick={handleGetProgress} disabled={isLoading} className="w-full mb-4">
                {isLoading ? 'Loading...' : 'Get Progress Report'}
              </Button>
              
              {progressData && (
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Overall Progress</span>
                      <span>{Math.round(progressData.overallProgress * 100)}%</span>
                    </div>
                    <Progress value={progressData.overallProgress * 100} />
                  </div>

                  <div className="text-sm text-gray-600">
                    <div>Time Spent: {Math.round(progressData.performanceMetrics.timeSpent)} min</div>
                    <div>Phases Completed: {progressData.phaseProgress.filter((p: any) => p.completed).length}/9</div>
                  </div>

                  {progressData.recommendations.length > 0 && (
                    <div>
                      <h4 className="font-medium text-sm mb-2">Recommendations:</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {progressData.recommendations.map((rec: string, index: number) => (
                          <li key={index}>• {rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {error && (
        <div className="text-red-600 bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}
    </div>
  );
}