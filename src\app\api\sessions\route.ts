import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { requireAuth } from '@/lib/clerk-sync';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    const userId = user.id;

    const sessions = await db.studySession.findMany({
      where: { userId },
      include: {
        topic: true,
        conversation: {
          include: {
            messages: {
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        }
      },
      orderBy: { startTime: 'desc' }
    });

    return NextResponse.json({ sessions });

  } catch (error) {
    console.error('Get sessions error:', error);
    if (error instanceof Error && error.message === "Authentication required") {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const userId = user.id;
    const { title, description, topicId } = await request.json();

    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }

    // End any active sessions
    await db.studySession.updateMany({
      where: { userId, isActive: true },
      data: { isActive: false, endTime: new Date() }
    });

    // Create new session
    const session = await db.studySession.create({
      data: {
        title,
        description,
        topicId,
        userId,
        isActive: true,
        startTime: new Date()
      },
      include: {
        topic: true,
        conversation: true
      }
    });

    return NextResponse.json({ session });

  } catch (error) {
    console.error('Create session error:', error);
    if (error instanceof Error && error.message === "Authentication required") {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}