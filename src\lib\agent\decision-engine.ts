import { 
  AgentState, 
  DecisionCriteria, 
  AgentAction,
  AgentPhaseType,
  ExplanationStep
} from './types';

export class DecisionEngine {
  private actionGenerators: Map<string, (state: AgentState, criteria: DecisionCriteria) => AgentAction[]> = new Map();

  constructor() {
    this.initializeActionGenerators();
  }

  private initializeActionGenerators() {
    // Planning Phase Actions
    this.actionGenerators.set('planning', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      actions.push({
        type: 'message',
        content: `I'll help you create a personalized learning plan for ${state.currentTopic}. Let me understand your goals and current knowledge level.`,
        confidence: 0.9,
        expectedResponse: 'User provides learning goals and background',
      });

      return actions;
    });

    // Assessment Phase Actions
    this.actionGenerators.set('assessment', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      actions.push({
        type: 'quiz',
        content: `Let me assess your current understanding of ${state.currentTopic} with a few questions.`,
        confidence: 0.8,
        metadata: {
          questionCount: 3,
          difficulty: state.difficulty,
        },
      });

      return actions;
    });

    // Explanation Phase Actions
    this.actionGenerators.set('explanation', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      const explanationPlan = this.generateExplanationPlan(state, criteria);
      
      actions.push({
        type: 'explanation',
        content: this.generateExplanationContent(state, criteria, explanationPlan),
        confidence: 0.8,
        metadata: {
          style: 'todo-list',
          difficulty: state.difficulty,
          explanationPlan,
          includeExamples: true,
          interactive: true,
        },
      });

      return actions;
    });

    // Practice Phase Actions
    this.actionGenerators.set('practice', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      actions.push({
        type: 'practice',
        content: `Now let's practice what you've learned about ${state.currentTopic}.`,
        confidence: 0.8,
        metadata: {
          difficulty: state.difficulty,
          exerciseCount: 3,
          adaptive: true,
        },
      });

      return actions;
    });

    // Q&A Phase Actions
    this.actionGenerators.set('qa', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      actions.push({
        type: 'message',
        content: `I see you have questions about ${state.currentTopic}. What would you like to know more about?`,
        confidence: 0.9,
        expectedResponse: 'User asks specific questions',
      });

      return actions;
    });

    // Feedback Phase Actions
    this.actionGenerators.set('feedback', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      const accuracy = state.userPerformance.correctAnswers / Math.max(state.userPerformance.totalQuestions, 1);
      const feedbackMessage = this.generateFeedbackMessage(accuracy, criteria);
      
      actions.push({
        type: 'feedback',
        content: feedbackMessage,
        confidence: 0.8,
        metadata: {
          accuracy,
          performanceTrend: criteria.performanceTrend,
          suggestions: this.generateSuggestions(state, criteria),
        },
      });

      return actions;
    });

    // Adaptation Phase Actions
    this.actionGenerators.set('adaptation', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      const adaptation = this.determineAdaptation(state, criteria);
      
      actions.push({
        type: 'adaptation',
        content: `I notice we need to adjust our approach. ${adaptation.message}`,
        confidence: 0.7,
        metadata: {
          adaptation: adaptation.type,
          newDifficulty: adaptation.newDifficulty,
          reason: adaptation.reason,
        },
      });

      return actions;
    });

    // Reflection Phase Actions
    this.actionGenerators.set('reflection', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      const reflection = this.generateReflection(state, criteria);
      
      actions.push({
        type: 'message',
        content: `Let's reflect on what we've learned about ${state.currentTopic}. ${reflection}`,
        confidence: 0.8,
        expectedResponse: 'User reflects on learning',
      });

      return actions;
    });

    // Completion Phase Actions
    this.actionGenerators.set('completion', (state, criteria) => {
      const actions: AgentAction[] = [];
      
      const completion = this.generateCompletionMessage(state, criteria);
      
      actions.push({
        type: 'message',
        content: completion,
        confidence: 0.9,
        expectedResponse: 'User acknowledges completion',
      });

      return actions;
    });
  }

  public generateActions(state: AgentState, criteria: DecisionCriteria): AgentAction[] {
    const generator = this.actionGenerators.get(state.currentPhase);
    if (!generator) {
      return [];
    }

    return generator(state, criteria);
  }

  public selectBestAction(actions: AgentAction[]): AgentAction | null {
    if (actions.length === 0) {
      return null;
    }

    // Select action with highest confidence
    return actions.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );
  }

  private getExplanationStyle(state: AgentState, criteria: DecisionCriteria): string {
    if (state.difficulty === 'beginner') {
      return 'in simple terms with everyday examples';
    } else if (state.difficulty === 'advanced') {
      return 'with technical details and advanced concepts';
    } else {
      return 'with clear explanations and relevant examples';
    }
  }

  private generateExplanationPlan(state: AgentState, criteria: DecisionCriteria): ExplanationStep[] {
    const topic = state.currentTopic.toLowerCase();
    const difficulty = state.difficulty;
    const understanding = criteria.userUnderstanding;

    // Base steps for any topic
    const baseSteps: ExplanationStep[] = [
      {
        id: 'concept-intro',
        title: '🎯 Understand the Core Concept',
        description: `Grasp the fundamental definition and importance of ${state.currentTopic}`,
        priority: 1,
        estimatedTime: 3,
        difficulty: 'beginner',
        interactive: true,
        examples: [],
        checkpoints: ['Can define the concept in simple terms', 'Understands why it matters']
      },
      {
        id: 'key-components',
        title: '🔍 Identify Key Components',
        description: `Break down ${state.currentTopic} into its essential parts and elements`,
        priority: 2,
        estimatedTime: 4,
        difficulty: 'intermediate',
        interactive: true,
        examples: [],
        checkpoints: ['Can list main components', 'Understands how components relate']
      },
      {
        id: 'real-world',
        title: '🌍 Connect to Real-World Applications',
        description: `Explore how ${state.currentTopic} is used in everyday life and industry`,
        priority: 3,
        estimatedTime: 3,
        difficulty: 'beginner',
        interactive: true,
        examples: [],
        checkpoints: ['Can identify real-world examples', 'Understands practical value']
      }
    ];

    // Topic-specific steps
    let topicSpecificSteps: ExplanationStep[] = [];

    if (topic.includes('machine learning') || topic.includes('ml') || topic.includes('ai')) {
      topicSpecificSteps = [
        {
          id: 'ml-types',
          title: '🤖 Learn ML Types & Approaches',
          description: 'Understand supervised, unsupervised, and reinforcement learning',
          priority: 4,
          estimatedTime: 5,
          difficulty: 'intermediate',
          interactive: true,
          examples: ['Spam detection (supervised)', 'Customer segmentation (unsupervised)', 'Game playing (reinforcement)'],
          checkpoints: ['Can differentiate between ML types', 'Knows when to use each type']
        },
        {
          id: 'ml-workflow',
          title: '⚙️ Master the ML Workflow',
          description: 'Learn the step-by-step process of building ML models',
          priority: 5,
          estimatedTime: 4,
          difficulty: 'intermediate',
          interactive: true,
          examples: ['Data collection → Cleaning → Training → Testing → Deployment'],
          checkpoints: ['Understands end-to-end ML process', 'Knows key stages']
        }
      ];
    } else if (topic.includes('programming') || topic.includes('coding') || topic.includes('algorithm')) {
      topicSpecificSteps = [
        {
          id: 'programming-basics',
          title: '💻 Master Programming Fundamentals',
          description: 'Learn variables, data types, and control structures',
          priority: 4,
          estimatedTime: 5,
          difficulty: 'beginner',
          interactive: true,
          examples: ['Variables (x = 5)', 'Loops (for/while)', 'Conditionals (if/else)'],
          checkpoints: ['Understands basic programming concepts', 'Can write simple code']
        },
        {
          id: 'algorithm-thinking',
          title: '🧠 Develop Algorithmic Thinking',
          description: 'Learn how to break down problems into step-by-step solutions',
          priority: 5,
          estimatedTime: 4,
          difficulty: 'intermediate',
          interactive: true,
          examples: ['Recipe steps', 'Math problem solving', 'Daily routines'],
          checkpoints: ['Can think algorithmically', 'Can solve problems step-by-step']
        }
      ];
    } else if (topic.includes('math') || topic.includes('calculus') || topic.includes('algebra')) {
      topicSpecificSteps = [
        {
          id: 'math-foundations',
          title: '📊 Build Mathematical Foundations',
          description: 'Understand the basic principles and formulas',
          priority: 4,
          estimatedTime: 5,
          difficulty: 'intermediate',
          interactive: true,
          examples: ['Basic operations', 'Equations', 'Functions'],
          checkpoints: ['Knows key formulas', 'Understands mathematical principles']
        },
        {
          id: 'problem-solving',
          title: '🎯 Practice Problem-Solving Strategies',
          description: 'Learn systematic approaches to solving math problems',
          priority: 5,
          estimatedTime: 4,
          difficulty: 'intermediate',
          interactive: true,
          examples: ['Word problems', 'Equation solving', 'Graph analysis'],
          checkpoints: ['Can solve problems systematically', 'Understands problem-solving strategies']
        }
      ];
    }

    // Adaptive difficulty adjustment
    if (understanding < 0.4) {
      // Add more foundational steps for struggling learners
      const foundationalSteps: ExplanationStep[] = [
        {
          id: 'prerequisites',
          title: '📚 Review Prerequisites',
          description: 'Ensure you have the foundational knowledge needed',
          priority: 0,
          estimatedTime: 3,
          difficulty: 'beginner',
          interactive: true,
          examples: [],
          checkpoints: ['Has necessary background knowledge', 'Ready for main content']
        }
      ];
      return [...foundationalSteps, ...baseSteps, ...topicSpecificSteps];
    } else if (understanding > 0.7) {
      // Add advanced steps for quick learners
      const advancedSteps: ExplanationStep[] = [
        {
          id: 'advanced-concepts',
          title: '🚀 Explore Advanced Concepts',
          description: 'Dive deeper into complex aspects and cutting-edge applications',
          priority: 10,
          estimatedTime: 5,
          difficulty: 'advanced',
          interactive: true,
          examples: [],
          checkpoints: ['Understands advanced topics', 'Can discuss complex applications']
        }
      ];
      return [...baseSteps, ...topicSpecificSteps, ...advancedSteps];
    }

    return [...baseSteps, ...topicSpecificSteps];
  }

  private generateExplanationContent(state: AgentState, criteria: DecisionCriteria, plan: ExplanationStep[]): string {
    const totalTime = plan.reduce((sum, step) => sum + step.estimatedTime, 0);
    const completedSteps = 0; // Will be tracked as user progresses
    
    let content = `# 📋 Learning Plan: ${state.currentTopic}\n\n`;
    content += `**Estimated Time:** ${totalTime} minutes | **Difficulty:** ${state.difficulty} | **Steps:** ${plan.length}\n\n`;
    
    content += `---\n\n`;
    
    content += `## 🎯 Your Learning To-Do List\n\n`;
    content += `I'll guide you through understanding ${state.currentTopic} using this step-by-step approach. `;
    content += `Each step builds on the previous one, ensuring you master the concept systematically.\n\n`;
    
    // Group steps by priority ranges
    const priorityGroups = this.groupStepsByPriority(plan);
    
    Object.entries(priorityGroups).forEach(([priorityRange, steps]) => {
      content += `### ${this.getPriorityGroupTitle(priorityRange)}\n\n`;
      
      steps.forEach((step, index) => {
        const stepNumber = plan.indexOf(step) + 1;
        const statusIcon = this.getStepStatusIcon(step, completedSteps);
        const difficultyBadge = this.getDifficultyBadge(step.difficulty);
        
        content += `${statusIcon} **Step ${stepNumber}: ${step.title}** ${difficultyBadge}\n`;
        content += `${step.description}\n`;
        content += `⏱️ **Time:** ${step.estimatedTime} min | 📊 **Priority:** ${step.priority}\n`;
        
        if (step.examples.length > 0) {
          content += `💡 **Examples:** ${step.examples.join(', ')}\n`;
        }
        
        content += `✅ **Success Checkpoints:** ${step.checkpoints.join(' • ')}\n\n`;
      });
      
      content += `---\n\n`;
    });
    
    content += `## 🚀 How to Use This Learning Plan\n\n`;
    content += `1. **Follow the Order**: Complete steps in priority order (lowest number first)\n`;
    content += `2. **Interactive Learning**: Each step includes interactive elements and examples\n`;
    content += `3. **Self-Check**: Use the success checkpoints to verify your understanding\n`;
    content += `4. **Ask Questions**: If anything is unclear, I'm here to help!\n\n`;
    
    content += `## 📈 Progress Tracking\n\n`;
    content += `I'll track your progress as you complete each step. You can always ask:\n`;
    content += `- *"What's my current progress?"*\n`;
    content += `- *"Explain step [X] in more detail"*\n`;
    content += `- *"Give me more examples for step [X]"*\n\n`;
    
    content += `**Ready to start?** Let's begin with Step 1! 🎯\n`;
    
    return content;
  }

  private groupStepsByPriority(plan: ExplanationStep[]): Record<string, ExplanationStep[]> {
    const groups: Record<string, ExplanationStep[]> = {};
    
    plan.forEach(step => {
      let range: string;
      if (step.priority <= 3) range = '🌟 Core Foundation';
      else if (step.priority <= 6) range = '🔧 Building Knowledge';
      else if (step.priority <= 8) range = '🚀 Advanced Concepts';
      else range = '🎓 Expert Level';
      
      if (!groups[range]) groups[range] = [];
      groups[range].push(step);
    });
    
    return groups;
  }

  private getPriorityGroupTitle(range: string): string {
    const titles: Record<string, string> = {
      '🌟 Core Foundation': '🌟 Core Foundation (Must-Know Basics)',
      '🔧 Building Knowledge': '🔧 Building Knowledge (Essential Concepts)',
      '🚀 Advanced Concepts': '🚀 Advanced Concepts (Deep Understanding)',
      '🎓 Expert Level': '🎓 Expert Level (Specialized Knowledge)'
    };
    return titles[range] || range;
  }

  private getStepStatusIcon(step: ExplanationStep, completedSteps: number): string {
    // This would normally check if the step is completed
    return '⏳'; // Pending
  }

  private getDifficultyBadge(difficulty: string): string {
    const badges: Record<string, string> = {
      'beginner': '🟢 Beginner',
      'intermediate': '🟡 Intermediate',
      'advanced': '🔴 Advanced'
    };
    return `(${badges[difficulty] || difficulty})`;
  }

  private generateFeedbackMessage(accuracy: number, criteria: DecisionCriteria): string {
    if (accuracy > 0.8) {
      return 'Excellent work! You\'re demonstrating a strong understanding of the material.';
    } else if (accuracy > 0.6) {
      return 'Good job! You\'re making solid progress, and with a bit more practice, you\'ll master this.';
    } else if (accuracy > 0.4) {
      return 'You\'re making progress, but let\'s focus on strengthening your understanding of the key concepts.';
    } else {
      return 'I can see this is challenging, but don\'t worry! Let\'s try a different approach to help you understand better.';
    }
  }

  private generateSuggestions(state: AgentState, criteria: DecisionCriteria): string[] {
    const suggestions: string[] = [];
    
    if (criteria.userUnderstanding < 0.5) {
      suggestions.push('Review the fundamental concepts again');
      suggestions.push('Try explaining the concepts in your own words');
    }
    
    if (criteria.userEngagement < 0.5) {
      suggestions.push('Take short breaks to maintain focus');
      suggestions.push('Try relating the material to your interests');
    }
    
    if (criteria.performanceTrend === 'declining') {
      suggestions.push('Review previous material before continuing');
      suggestions.push('Ask for clarification on confusing points');
    }

    return suggestions;
  }

  private determineAdaptation(state: AgentState, criteria: DecisionCriteria): {
    type: string;
    message: string;
    newDifficulty?: string;
    reason: string;
  } {
    if (criteria.userUnderstanding < 0.3) {
      return {
        type: 'simplify',
        message: 'Let\'s break this down into simpler concepts and start with the basics.',
        newDifficulty: 'beginner',
        reason: 'Low understanding detected',
      };
    } else if (criteria.userEngagement < 0.3) {
      return {
        type: 'engagement',
        message: 'Let\'s try a more interactive approach with examples and activities.',
        reason: 'Low engagement detected',
      };
    } else if (criteria.performanceTrend === 'declining') {
      return {
        type: 'review',
        message: 'Let\'s review the previous material to ensure we have a solid foundation.',
        reason: 'Performance declining',
      };
    } else {
      return {
        type: 'pace',
        message: 'Let\'s adjust our pace to better match your learning speed.',
        reason: 'Pace adjustment needed',
      };
    }
  }

  private generateReflection(state: AgentState, criteria: DecisionCriteria): string {
    const progress = this.calculateProgress(state);
    const strengths = this.identifyStrengths(state, criteria);
    const areas = this.identifyImprovementAreas(state, criteria);
    
    return `You've made ${progress > 0.7 ? 'excellent' : progress > 0.5 ? 'good' : 'steady'} progress. ` +
           `Your strengths include ${strengths.join(', ')}. ` +
           `Areas for further improvement include ${areas.join(', ')}. ` +
           `What aspects would you like to focus on next?`;
  }

  private generateCompletionMessage(state: AgentState, criteria: DecisionCriteria): string {
    const accuracy = state.userPerformance.correctAnswers / Math.max(state.userPerformance.totalQuestions, 1);
    const timeSpent = Math.floor((Date.now() - state.metadata.startTime.getTime()) / 60000);
    
    return `Congratulations! You've completed your learning session on ${state.currentTopic}. ` +
           `You answered ${state.userPerformance.correctAnswers} out of ${state.userPerformance.totalQuestions} questions correctly ` +
           `(${Math.round(accuracy * 100)}% accuracy) over ${timeSpent} minutes. ` +
           `Would you like to continue with more advanced topics or review what we've covered?`;
  }

  private calculateProgress(state: AgentState): number {
    const understandingProgress = state.userPerformance.understandingLevel;
    const engagementProgress = state.userPerformance.engagementScore;
    const phaseProgress = state.phaseHistory.length / 9;
    
    return (understandingProgress + engagementProgress + phaseProgress) / 3;
  }

  private identifyStrengths(state: AgentState, criteria: DecisionCriteria): string[] {
    const strengths: string[] = [];
    
    if (criteria.userUnderstanding > 0.7) {
      strengths.push('conceptual understanding');
    }
    
    if (criteria.userEngagement > 0.7) {
      strengths.push('active participation');
    }
    
    if (state.userPerformance.correctAnswers / Math.max(state.userPerformance.totalQuestions, 1) > 0.7) {
      strengths.push('problem-solving');
    }

    return strengths;
  }

  private identifyImprovementAreas(state: AgentState, criteria: DecisionCriteria): string[] {
    const areas: string[] = [];
    
    if (criteria.userUnderstanding < 0.5) {
      areas.push('conceptual clarity');
    }
    
    if (criteria.userEngagement < 0.5) {
      areas.push('consistent engagement');
    }
    
    if (state.userPerformance.correctAnswers / Math.max(state.userPerformance.totalQuestions, 1) < 0.5) {
      areas.push('practice application');
    }

    return areas;
  }

  public analyzeUserResponse(state: AgentState, userMessage: string, responseTime: number): DecisionCriteria {
    // Analyze user response to update decision criteria
    const understanding = this.analyzeUnderstanding(state, userMessage);
    const engagement = this.analyzeEngagement(state, userMessage, responseTime);
    const complexity = this.analyzeComplexity(userMessage);
    const questionFreq = this.analyzeQuestionFrequency(state, userMessage);
    const trend = this.analyzePerformanceTrend(state);
    const feedback = this.extractFeedback(userMessage);

    return {
      userUnderstanding: understanding,
      userEngagement: engagement,
      topicComplexity: complexity,
      timeSpent: (Date.now() - state.metadata.startTime.getTime()) / 60000, // minutes
      questionFrequency: questionFreq,
      performanceTrend: trend,
      userFeedback: feedback,
    };
  }

  private analyzeUnderstanding(state: AgentState, message: string): number {
    // Simple heuristic for understanding analysis
    const messageLower = message.toLowerCase();
    const understandingIndicators = ['i understand', 'i get it', 'that makes sense', 'clear', 'got it'];
    const confusionIndicators = ['i don\'t understand', 'confused', 'unclear', 'what do you mean'];
    
    let understandingScore = 0.5; // baseline
    
    understandingIndicators.forEach(indicator => {
      if (messageLower.includes(indicator)) understandingScore += 0.2;
    });
    
    confusionIndicators.forEach(indicator => {
      if (messageLower.includes(indicator)) understandingScore -= 0.2;
    });
    
    return Math.max(0, Math.min(1, understandingScore));
  }

  private analyzeEngagement(state: AgentState, message: string, responseTime: number): number {
    // Analyze engagement based on response time and message content
    let engagementScore = 0.5; // baseline
    
    // Quick responses indicate higher engagement
    if (responseTime < 30000) engagementScore += 0.2; // less than 30 seconds
    if (responseTime > 120000) engagementScore -= 0.2; // more than 2 minutes
    
    // Message length indicates engagement
    if (message.length > 50) engagementScore += 0.1;
    if (message.length > 100) engagementScore += 0.1;
    
    // Questions indicate engagement
    if (message.includes('?')) engagementScore += 0.1;
    
    return Math.max(0, Math.min(1, engagementScore));
  }

  private analyzeComplexity(message: string): number {
    // Analyze complexity of user's question or response
    const complexWords = ['explain', 'why', 'how', 'compare', 'contrast', 'analyze', 'synthesize'];
    const messageLower = message.toLowerCase();
    
    let complexityScore = 0.3; // baseline
    
    complexWords.forEach(word => {
      if (messageLower.includes(word)) complexityScore += 0.1;
    });
    
    // Longer messages tend to be more complex
    if (message.length > 100) complexityScore += 0.1;
    if (message.length > 200) complexityScore += 0.1;
    
    return Math.max(0, Math.min(1, complexityScore));
  }

  private analyzeQuestionFrequency(state: AgentState, message: string): number {
    // Calculate frequency of questions in recent conversation
    const recentMessages = state.context.conversationHistory.slice(-5);
    const questionCount = recentMessages.filter(msg => 
      msg.role === 'user' && msg.content.includes('?')
    ).length;
    
    return questionCount / recentMessages.length;
  }

  private analyzePerformanceTrend(state: AgentState): 'improving' | 'stable' | 'declining' {
    // Analyze performance trend based on recent interactions
    const recentAccuracy = this.calculateRecentAccuracy(state, 3);
    const olderAccuracy = this.calculateRecentAccuracy(state, 6, 3);
    
    if (recentAccuracy > olderAccuracy + 0.1) return 'improving';
    if (recentAccuracy < olderAccuracy - 0.1) return 'declining';
    return 'stable';
  }

  private calculateRecentAccuracy(state: AgentState, recentCount: number, offset: number = 0): number {
    // Calculate accuracy for recent interactions
    const recentMessages = state.context.conversationHistory.slice(-(recentCount + offset), -offset);
    const correctResponses = recentMessages.filter(msg => 
      msg.role === 'assistant' && msg.content.toLowerCase().includes('correct')
    ).length;
    
    return correctResponses / Math.max(recentMessages.length, 1);
  }

  private extractFeedback(message: string): string[] {
    // Extract explicit feedback from user message
    const feedback: string[] = [];
    const messageLower = message.toLowerCase();
    
    if (messageLower.includes('too fast') || messageLower.includes('slow down')) {
      feedback.push('pace_too_fast');
    }
    
    if (messageLower.includes('too slow') || messageLower.includes('speed up')) {
      feedback.push('pace_too_slow');
    }
    
    if (messageLower.includes('confusing') || messageLower.includes('unclear')) {
      feedback.push('content_unclear');
    }
    
    if (messageLower.includes('helpful') || messageLower.includes('good')) {
      feedback.push('content_helpful');
    }
    
    return feedback;
  }
}