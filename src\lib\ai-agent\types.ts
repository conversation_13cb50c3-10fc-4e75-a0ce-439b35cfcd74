import { z } from 'zod';

// Learning phases
export enum LearningPhase {
  PLANNING = 'planning',
  EXPLANATION = 'explanation',
  QA = 'qa',
  PRACTICE = 'practice',
  ASSESSMENT = 'assessment',
  COMPLETION = 'completion'
}

// Learning step types
export enum StepType {
  INTRODUCTION = 'introduction',
  CONCEPT = 'concept',
  EXAMPLE = 'example',
  ANALOGY = 'analogy',
  PRACTICE = 'practice',
  QUIZ = 'quiz',
  SUMMARY = 'summary',
  ADVANCED = 'advanced'
}

// User response types
export enum UserResponseType {
  PROCEED = 'proceed',
  QUESTION = 'question',
  CLARIFICATION = 'clarification',
  SKIP = 'skip',
  REPEAT = 'repeat',
  HELP = 'help'
}

// Curriculum step schema
export const CurriculumStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  type: z.nativeEnum(StepType),
  content: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
  estimatedTime: z.string(),
  prerequisites: z.array(z.string()).default([]),
  dependencies: z.array(z.string()).default([]),
  isCompleted: z.boolean().default(false),
  resources: z.array(z.object({
    type: z.string(),
    title: z.string(),
    url: z.string().optional(),
    description: z.string().optional()
  })).default([])
});

// Curriculum schema
export const CurriculumSchema = z.object({
  id: z.string(),
  topic: z.string(),
  subject: z.string(),
  description: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
  estimatedTotalTime: z.string(),
  steps: z.array(CurriculumStepSchema),
  learningObjectives: z.array(z.string()),
  prerequisites: z.array(z.string()),
  createdAt: z.date(),
  updatedAt: z.date()
});

// Learning session schema
export const LearningSessionSchema = z.object({
  id: z.string(),
  curriculumId: z.string(),
  currentPhase: z.nativeEnum(LearningPhase),
  currentStepIndex: z.number(),
  userResponses: z.array(z.object({
    stepId: z.string(),
    response: z.string(),
    responseType: z.nativeEnum(UserResponseType),
    timestamp: z.date(),
    wasHelpful: z.boolean().optional()
  })),
  progress: z.number(), // 0-100
  startTime: z.date(),
  endTime: z.date().optional(),
  isCompleted: z.boolean().default(false),
  adaptiveDifficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate')
});

// AI Agent message schema
export const AgentMessageSchema = z.object({
  id: z.string(),
  type: z.enum(['curriculum', 'explanation', 'question', 'practice', 'feedback', 'guidance']),
  content: z.string(),
  phase: z.nativeEnum(LearningPhase),
  stepId: z.string().optional(),
  metadata: z.object({
    suggestedActions: z.array(z.string()).optional(),
    confidence: z.number().optional(),
    nextStepHint: z.string().optional(),
    learningTips: z.array(z.string()).optional()
  }).optional(),
  timestamp: z.date()
});

// User interaction schema
export const UserInteractionSchema = z.object({
  sessionId: z.string(),
  message: z.string(),
  responseType: z.nativeEnum(UserResponseType),
  targetStepId: z.string().optional(),
  timestamp: z.date()
});

// Export types
export type CurriculumStep = z.infer<typeof CurriculumStepSchema>;
export type Curriculum = z.infer<typeof CurriculumSchema>;
export type LearningSession = z.infer<typeof LearningSessionSchema>;
export type AgentMessage = z.infer<typeof AgentMessageSchema>;
export type UserInteraction = z.infer<typeof UserInteractionSchema>;