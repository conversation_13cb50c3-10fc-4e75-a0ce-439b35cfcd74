import { NextRequest, NextResponse } from 'next/server';
import { runCreateStudyPlan } from '@/lib/genkit/study-flows';

export async function POST(request: NextRequest) {
  try {
    const { subject, goals, timeAvailable, currentLevel, preferences } = await request.json();

    if (!subject || !goals || !timeAvailable) {
      return NextResponse.json({ 
        error: 'Subject, goals, and timeAvailable are required' 
      }, { status: 400 });
    }

    // Generate study plan using Genkit flow
    const studyPlanResponse = await runCreateStudyPlan({
      subject,
      goals,
      timeAvailable,
      currentLevel: currentLevel || 'intermediate',
      preferences: preferences || {},
    });

    return NextResponse.json({
      studyPlan: studyPlanResponse.studyPlan,
      milestones: studyPlanResponse.milestones,
      tips: studyPlanResponse.tips,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Study plan API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}