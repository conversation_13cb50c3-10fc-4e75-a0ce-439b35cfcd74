import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Target, MessageSquare, Users, BarChart3, <PERSON>, <PERSON>rk<PERSON>, ArrowRight } from "lucide-react";
import Link from "next/link";
import { SignedIn, SignedOut, SignInButton, SignUpButton } from "@clerk/nextjs";

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted">
      {/* Hero Section */}
      <section className="relative overflow-hidden px-4 py-20 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-grid-slate-100/[0.05] bg-[size:20px_20px]" />
        <div className="relative mx-auto max-w-7xl">
          <div className="text-center">
            <Badge variant="secondary" className="mb-4">
              <Sparkles className="mr-2 h-4 w-4" />
              AI-Powered Learning
            </Badge>
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Transform Your Study Sessions
              <span className="block text-primary"> with AI</span>
            </h1>
            <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-muted-foreground">
              Experience the future of learning with our AI-powered study assistant. 
              Get personalized help, track your progress, and master any subject through engaging conversations.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <SignedIn>
                <Link href="/app">
                  <Button size="lg" className="px-8">
                    Start Studying
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </SignedIn>
              <SignedOut>
                <SignUpButton mode="modal">
                  <Button size="lg" className="px-8">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </SignUpButton>
                <SignInButton mode="modal">
                  <Button variant="outline" size="lg" className="px-8">
                    Sign In
                  </Button>
                </SignInButton>
              </SignedOut>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-4 py-24 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Everything You Need to Excel
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
              Powerful features designed to make learning more effective and enjoyable.
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-none shadow-lg">
              <CardHeader>
                <BookOpen className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">AI Study Assistant</CardTitle>
                <CardDescription>
                  Chat with an intelligent AI tutor that understands your learning style and adapts to your needs.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <Target className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">Session Management</CardTitle>
                <CardDescription>
                  Create focused study sessions with specific topics, goals, and track your time spent learning.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <MessageSquare className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">Real-Time Chat</CardTitle>
                <CardDescription>
                  Experience instant responses with live typing indicators and seamless conversation flow.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">Progress Tracking</CardTitle>
                <CardDescription>
                  Monitor your learning progress, track study time, and see your improvement over time.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <Users className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">Topic Organization</CardTitle>
                <CardDescription>
                  Explore subjects across STEM, Humanities, and Social Sciences with structured learning paths.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <Clock className="h-8 w-8 text-primary" />
                <CardTitle className="text-xl">Study Anywhere</CardTitle>
                <CardDescription>
                  Access your study sessions from any device with our responsive design and mobile-friendly interface.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-muted/50 px-4 py-24 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              How It Works
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
              Get started in minutes and transform your learning experience.
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-2xl font-bold">
                1
              </div>
              <h3 className="mt-6 text-xl font-semibold">Create a Study Session</h3>
              <p className="mt-2 text-muted-foreground">
                Start by creating a new study session with your chosen topic and learning goals.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-2xl font-bold">
                2
              </div>
              <h3 className="mt-6 text-xl font-semibold">Chat with AI</h3>
              <p className="mt-2 text-muted-foreground">
                Ask questions, get explanations, and receive personalized help from your AI study assistant.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-2xl font-bold">
                3
              </div>
              <h3 className="mt-6 text-xl font-semibold">Track Progress</h3>
              <p className="mt-2 text-muted-foreground">
                Monitor your learning journey and see how you improve over time with detailed progress tracking.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-4 py-24 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Ready to Transform Your Learning?
          </h2>
          <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
            Join thousands of students who are already learning smarter with AI. 
            Start your journey to academic excellence today.
          </p>
          <div className="mt-10">
            <SignedIn>
              <Link href="/app">
                <Button size="lg" className="px-12 py-3 text-lg">
                  Start Learning Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </SignedIn>
            <SignedOut>
              <SignUpButton mode="modal">
                <Button size="lg" className="px-12 py-3 text-lg">
                  Start Learning Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </SignUpButton>
            </SignedOut>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background px-4 py-12 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-between gap-8 md:flex-row">
            <div className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              <span className="text-lg font-semibold">StudyAI</span>
            </div>
            <div className="flex gap-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground">About</a>
              <a href="#" className="hover:text-foreground">Features</a>
              <a href="#" className="hover:text-foreground">Privacy</a>
              <a href="#" className="hover:text-foreground">Terms</a>
            </div>
            <div className="text-sm text-muted-foreground">
              © 2024 StudyAI. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}