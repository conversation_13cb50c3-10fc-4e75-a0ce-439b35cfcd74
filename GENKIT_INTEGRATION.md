# Genkit Integration for StudyAI

This document explains the Genkit integration implemented in the StudyAI platform.

## Overview

Genkit is a framework for building AI-powered applications with Google's Gemini models. This integration replaces the previous z-ai-web-dev-sdk with a more robust and feature-rich AI system.

## Features

### 1. Study Assistant Flow (`studyAssistantFlow`)
- **Location**: `src/lib/genkit/study-flows.ts`
- **Purpose**: Main chat interface for student interactions
- **Features**:
  - Context-aware conversations
  - Educational tool integration
  - Suggested actions extraction
  - Study tips generation
  - Multi-turn conversation support

### 2. Study Plan Creation (`createStudyPlanFlow`)
- **Location**: `src/lib/genkit/study-flows.ts`
- **Purpose**: Generate personalized study plans
- **Features**:
  - Customized based on student level and goals
  - Time-aware planning
  - Milestone tracking
  - Resource recommendations

### 3. Educational Tools
- **Location**: `src/lib/genkit/study-flows.ts`
- **Available Tools**:
  - `explainConcept`: Breaks down complex topics
  - `createQuiz`: Generates assessment questions
  - `summarizeContent`: Condenses study materials

### 4. Prompt Templates
- **Location**: `src/lib/genkit/prompts.ts`
- **Available Prompts**:
  - `explainConceptPrompt`: For concept explanations
  - `createQuizPrompt`: For quiz generation
  - `summarizeContentPrompt`: For content summarization
  - `studyTipsPrompt`: For personalized study advice
  - `interactiveLearningPrompt`: For engaging activities
  - `progressAssessmentPrompt`: For performance feedback

## API Endpoints

### 1. Chat API (`/api/chat`)
- **Method**: POST
- **Purpose**: Main chat interface using Genkit flows
- **Enhanced Features**:
  - Returns suggested actions
  - Provides study tips
  - Maintains conversation context

### 2. Study Plan API (`/api/study-plan`)
- **Method**: POST
- **Purpose**: Generate personalized study plans
- **Input**: Subject, goals, time available, current level, preferences

### 3. Tools API (`/api/tools`)
- **Method**: POST
- **Purpose**: Direct access to educational tools
- **Available Tools**: explainConcept, createQuiz, summarizeContent

### 4. Test API (`/api/test-genkit`)
- **Method**: GET
- **Purpose**: Test Genkit integration
- **Returns**: Sample response from the study assistant

## Frontend Enhancements

### 1. Suggested Actions
- Interactive buttons that appear based on AI recommendations
- Auto-fill and send suggested queries
- Located in the chat interface

### 2. Study Tips
- Context-aware study advice
- Visually highlighted in blue boxes
- Automatically updated with each response

### 3. Enhanced Message Handling
- Support for Genkit response structure
- Metadata storage for actions and tips
- Improved error handling

## Setup Instructions

### 1. Environment Configuration
Create a `.env.local` file with:
```env
GOOGLE_GENAI_API_KEY=your-google-ai-api-key-here
DATABASE_URL="file:./dev.db"
```

### 2. API Key Setup
1. Get a Google AI API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your `.env.local` file
3. Restart the development server

### 3. Testing the Integration
1. Navigate to `http://localhost:3000/api/test-genkit`
2. Check the response to verify Genkit is working
3. Test the chat interface in the main application

## Usage Examples

### 1. Basic Chat
```javascript
// The chat interface automatically uses Genkit flows
// No changes needed in the frontend code
```

### 2. Creating a Study Plan
```javascript
const response = await fetch('/api/study-plan', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    subject: 'Mathematics',
    goals: ['Learn calculus', 'Improve problem-solving'],
    timeAvailable: '2 hours per day',
    currentLevel: 'intermediate',
    preferences: {
      learningStyle: 'visual',
      studyDuration: '45 minutes'
    }
  })
});
```

### 3. Using Tools Directly
```javascript
const response = await fetch('/api/tools', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tool: 'explainConcept',
    params: {
      concept: 'Photosynthesis',
      difficulty: 'beginner',
      subject: 'Biology'
    }
  })
});
```

## Benefits of Genkit Integration

### 1. Enhanced AI Capabilities
- Access to Google's Gemini models
- Better context understanding
- More accurate educational responses

### 2. Structured Outputs
- Consistent response format
- Extractable suggested actions
- Categorized study tips

### 3. Tool Integration
- Dynamic tool usage based on context
- Extensible tool system
- Better educational features

### 4. Improved Performance
- Faster response times
- Better error handling
- More reliable connections

### 5. Developer Experience
- Type-safe flows
- Better debugging capabilities
- Comprehensive logging

## Troubleshooting

### 1. API Key Issues
- Ensure your Google AI API key is valid
- Check that the key has the necessary permissions
- Verify the key is properly set in `.env.local`

### 2. Import Errors
- Ensure all Genkit packages are installed
- Check that import paths are correct
- Restart the development server after making changes

### 3. Response Issues
- Check the test endpoint at `/api/test-genkit`
- Verify the database connection
- Look for error messages in the console

### 4. Frontend Issues
- Clear browser cache
- Check for JavaScript errors in the browser console
- Verify that the state management is working correctly

## Future Enhancements

### 1. Additional Tools
- Progress tracking tools
- Interactive exercise generators
- Multimedia content integration

### 2. Enhanced Prompts
- Subject-specific prompt templates
- Adaptive difficulty levels
- Personalized learning paths

### 3. Advanced Features
- Voice interaction support
- Multi-language support
- Collaborative study sessions

### 4. Analytics
- Learning progress tracking
- Study session analytics
- Performance metrics

## Conclusion

The Genkit integration significantly enhances the StudyAI platform by providing:
- More powerful AI capabilities
- Better educational features
- Improved user experience
- Extensible architecture

This integration positions StudyAI as a leading AI-powered educational platform with robust, scalable, and feature-rich functionality.