import ai from '@/lib/genkit/index';
import { gemini15Flash } from '@genkit-ai/googleai';
import { z } from 'zod';
import {
  explainConceptPrompt,
  createQuizPrompt,
  summarizeContentPrompt
} from './prompts';

// Define tools for educational features
// Define the main study assistant flow
export const studyAssistantFlow = ai.defineFlow(
  {
    name: 'studyAssistantFlow',
    inputSchema: z.object({
      message: z.string(),
      conversationHistory: z.array(z.object({
        role: z.enum(['user', 'model']),
        content: z.string(),
      })).optional(),
      sessionId: z.string().optional(),
      systemPrompt: z.string().optional(),
    }),
    outputSchema: z.object({
      response: z.string(),
      suggestedActions: z.array(z.string()).optional(),
      studyTips: z.array(z.string()).optional(),
    }),
  },
  async (input) => {
    const { message, conversationHistory = [], sessionId, systemPrompt } = input;

    // Prepare conversation history
    const messages = [
      {
        role: 'system' as const,
        content: [{ text: systemPrompt || `You are an expert AI study assistant. Your goal is to help students learn effectively.
        Be patient, encouraging, and adapt your teaching style to the student's needs.
        Break down complex topics into manageable parts and provide clear explanations.
        Use examples and analogies to make concepts easier to understand.

        When appropriate, use the available tools to provide enhanced learning experiences:
        - Use explainConcept when students ask about specific concepts
        - Use createQuiz when students want to test their knowledge
        - Use summarizeContent when students need concise summaries

        Always be supportive and celebrate student progress.` }],
      },
      ...conversationHistory.map(msg => ({
        role: msg.role as 'user' | 'model',
        content: [{ text: msg.content }],
      })),
      {
        role: 'user' as const,
        content: [{ text: message }],
      },
    ];

    // Generate response using Gemini
    const response = await ai.generate({
      model: gemini15Flash,
      messages,
      config: {
        temperature: 0.7,
        maxOutputTokens: 1000,
      },
    });

    const aiResponse = response.text;
    
    // Extract any suggested actions or study tips from the response
    const suggestedActions = extractSuggestedActions(aiResponse);
    const studyTips = extractStudyTips(aiResponse);

    return {
      response: aiResponse,
      suggestedActions,
      studyTips,
    };
  }
);

// Helper function to extract suggested actions from AI response
function extractSuggestedActions(response: string): string[] {
  const actions: string[] = [];
  
  // Look for patterns like "You can..." or "Try..." or "Consider..."
  const actionPatterns = [
    /(?:You can|Try|Consider|Let's|I recommend|I suggest)\s+([^.]*)\./gi,
    /(?:Would you like|Do you want)\s+([^?]*)\?/gi,
  ];

  actionPatterns.forEach(pattern => {
    const matches = response.match(pattern);
    if (matches) {
      actions.push(...matches.map(match => match.trim()));
    }
  });

  return actions.slice(0, 3); // Limit to 3 actions
}

// Helper function to extract study tips from AI response
function extractStudyTips(response: string): string[] {
  const tips: string[] = [];
  
  // Look for study tips in the response
  const tipPatterns = [
    /(?:Tip|Study tip|Remember|Note):\s*([^.]*)\./gi,
    /(?:Pro tip|Helpful hint):\s*([^.]*)\./gi,
  ];

  tipPatterns.forEach(pattern => {
    const matches = response.match(pattern);
    if (matches) {
      tips.push(...matches.map(match => match.replace(/^(Tip|Study tip|Remember|Note|Pro tip|Helpful hint):\s*/, '').trim()));
    }
  });

  return tips.slice(0, 3); // Limit to 3 tips
}

// Define a flow for creating personalized study plans
export const createStudyPlanFlow = ai.defineFlow(
  {
    name: 'createStudyPlanFlow',
    inputSchema: z.object({
      subject: z.string(),
      goals: z.array(z.string()),
      timeAvailable: z.string(),
      currentLevel: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
      preferences: z.object({
        learningStyle: z.enum(['visual', 'auditory', 'kinesthetic', 'reading']).optional(),
        studyDuration: z.string().optional(),
      }).optional(),
    }),
    outputSchema: z.object({
      studyPlan: z.string(),
      milestones: z.array(z.object({
        title: z.string(),
        description: z.string(),
        estimatedTime: z.string(),
        resources: z.array(z.string()),
      })),
      tips: z.array(z.string()),
    }),
  },
  async (input) => {
    const { subject, goals, timeAvailable, currentLevel, preferences } = input;

    const systemPrompt = `You are an expert study planner. Create a personalized study plan for a student who wants to learn ${subject}.
    
    Student Information:
    - Goals: ${goals.join(', ')}
    - Time Available: ${timeAvailable}
    - Current Level: ${currentLevel}
    - Learning Preferences: ${JSON.stringify(preferences || {})}
    
    Create a comprehensive study plan that includes:
    1. A detailed study plan with daily/weekly breakdown
    2. Key milestones with estimated timeframes
    3. Recommended resources for each milestone
    4. Personalized study tips based on their learning style
    
    Be specific, realistic, and encouraging.`;

    const response = await ai.generate({
      model: gemini15Flash,
      messages: [
        {
          role: 'system' as const,
          content: [{ text: systemPrompt }],
        },
        {
          role: 'user' as const,
          content: [{ text: `Please create a personalized study plan for me based on the information provided.` }],
        },
      ],
      config: {
        temperature: 0.7,
        maxOutputTokens: 2000,
      },
    });

    const aiResponse = response.text;
    
    // Parse the response to extract structured information
    const milestones = extractMilestones(aiResponse);
    const tips = extractStudyTips(aiResponse);

    return {
      studyPlan: aiResponse,
      milestones,
      tips,
    };
  }
);

// Helper function to extract milestones from study plan
function extractMilestones(response: string): Array<{
  title: string;
  description: string;
  estimatedTime: string;
  resources: string[];
}> {
  const milestones: Array<{
    title: string;
    description: string;
    estimatedTime: string;
    resources: string[];
  }> = [];

  // Look for milestone patterns in the response
  const milestonePattern = /(?:Milestone|Week|Phase|Stage)\s*\d+:\s*([^\n]*)\n\s*([^\n]*)\n\s*(?:Time|Duration|Estimated):\s*([^\n]*)/gi;
  
  const matches = response.match(milestonePattern);
  if (matches) {
    matches.forEach(match => {
      const parts = match.split('\n');
      if (parts.length >= 3) {
        const title = parts[0].replace(/^(?:Milestone|Week|Phase|Stage)\s*\d+:\s*/, '').trim();
        const description = parts[1].trim();
        const estimatedTime = parts[2].replace(/^(?:Time|Duration|Estimated):\s*/, '').trim();
        
        milestones.push({
          title,
          description,
          estimatedTime,
          resources: [], // Would need more sophisticated parsing to extract resources
        });
      }
    });
  }

  return milestones.slice(0, 5); // Limit to 5 milestones
}

// Study tools for direct API access
export const studyTools = {
  async explainConcept(params: {
    concept: string;
    subject?: string;
    level?: string;
    learningStyle?: string;
  }) {
    const prompt = explainConceptPrompt({
      concept: params.concept,
      subject: params.subject || 'general',
      level: params.level || 'intermediate',
      learningStyle: params.learningStyle
    });

    const response = await ai.generate({
      model: gemini15Flash,
      messages: [
        {
          role: 'system' as const,
          content: [{ text: prompt }],
        },
        {
          role: 'user' as const,
          content: [{ text: `Please explain the concept: ${params.concept}` }],
        },
      ],
      config: {
        temperature: 0.7,
        maxOutputTokens: 1000,
      },
    });

    return response.text;
  },

  async createQuiz(params: {
    topic: string;
    difficulty?: string;
    questionCount?: number;
    questionTypes?: string;
  }) {
    const prompt = createQuizPrompt({
      topic: params.topic,
      difficulty: params.difficulty || 'intermediate',
      questionCount: params.questionCount || 5,
      questionTypes: params.questionTypes
    });

    const response = await ai.generate({
      model: gemini15Flash,
      messages: [
        {
          role: 'system' as const,
          content: [{ text: prompt }],
        },
        {
          role: 'user' as const,
          content: [{ text: `Please create a quiz about: ${params.topic}` }],
        },
      ],
      config: {
        temperature: 0.7,
        maxOutputTokens: 1500,
      },
    });

    return response.text;
  },

  async summarizeContent(params: {
    content: string;
    maxLength?: number;
    focusAreas?: string;
  }) {
    const prompt = summarizeContentPrompt({
      content: params.content,
      maxLength: params.maxLength,
      focusAreas: params.focusAreas
    });

    const response = await ai.generate({
      model: gemini15Flash,
      messages: [
        {
          role: 'system' as const,
          content: [{ text: prompt }],
        },
        {
          role: 'user' as const,
          content: [{ text: `Please summarize this content: ${params.content}` }],
        },
      ],
      config: {
        temperature: 0.7,
        maxOutputTokens: 800,
      },
    });

    return response.text;
  }
};

// Export flow runners for external use
export const runStudyAssistant = (input: any) => studyAssistantFlow(input);
export const runCreateStudyPlan = (input: any) => createStudyPlanFlow(input);