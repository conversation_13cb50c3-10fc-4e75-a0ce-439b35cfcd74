import { 
  AgentState, 
  AgentPhaseType, 
  PhaseTransition, 
  DecisionCriteria,
  AgentAction,
  LearningPlan,
  AgentStateSchema,
  LearningPlanSchema
} from './types';
import { PhaseTransitionEngine } from './phase-engine';
import { DecisionEngine } from './decision-engine';
import { db } from '@/lib/db';

export class AutonomousLearningAgent {
  private state: AgentState;
  private phaseEngine: PhaseTransitionEngine;
  private decisionEngine: DecisionEngine;
  private learningPlan: LearningPlan | null = null;

  constructor(sessionId: string, topic: string, initialGoals: string[] = []) {
    this.phaseEngine = new PhaseTransitionEngine();
    this.decisionEngine = new DecisionEngine();
    
    // Initialize agent state
    this.state = AgentStateSchema.parse({
      sessionId,
      currentPhase: 'planning',
      phaseHistory: ['planning'],
      learningGoals: initialGoals,
      currentTopic: topic,
      difficulty: 'intermediate',
      userPerformance: {
        correctAnswers: 0,
        totalQuestions: 0,
        understandingLevel: 0.5,
        engagementScore: 0.5,
      },
      context: {
        conversationHistory: [],
        currentStep: 0,
        totalSteps: 0,
        learningObjectives: [],
        prerequisites: [],
      },
      metadata: {
        startTime: new Date(),
        lastActivity: new Date(),
        phaseTransitions: 0,
        adaptations: 0,
      },
    });
  }

  public async initialize(): Promise<AgentAction> {
    // Create initial learning plan
    this.learningPlan = await this.createLearningPlan();
    
    // Generate initial action
    const criteria = this.getCurrentDecisionCriteria();
    const actions = this.decisionEngine.generateActions(this.state, criteria);
    const action = this.decisionEngine.selectBestAction(actions);
    
    return action || {
      type: 'message',
      content: `Welcome! I'm your AI learning assistant. Let's start learning about ${this.state.currentTopic}.`,
      confidence: 0.8,
    };
  }

  public async processUserMessage(userMessage: string): Promise<{
    action: AgentAction;
    phaseTransition?: PhaseTransition;
    stateUpdate: Partial<AgentState>;
  }> {
    const responseTime = this.calculateResponseTime();
    
    // Update conversation history
    this.updateConversationHistory('user', userMessage);
    
    // Analyze user response
    const criteria = this.decisionEngine.analyzeUserResponse(
      this.state, 
      userMessage, 
      responseTime
    );
    
    // Update state based on user response
    const stateUpdate = this.updateStateFromUserResponse(userMessage, criteria);
    
    // Check if phase transition is needed
    let phaseTransition: PhaseTransition | undefined;
    if (this.phaseEngine.shouldTransition(this.state, criteria)) {
      phaseTransition = this.executePhaseTransition(criteria);
    }
    
    // Generate next action
    const actions = this.decisionEngine.generateActions(this.state, criteria);
    const action = this.decisionEngine.selectBestAction(actions);
    
    // Persist state to database
    await this.persistState();
    
    return {
      action: action || {
        type: 'message',
        content: 'I understand. Let me help you with that.',
        confidence: 0.7,
      },
      phaseTransition,
      stateUpdate,
    };
  }

  public async processActionResult(action: AgentAction, result: any): Promise<AgentAction> {
    // Update conversation history with assistant response
    this.updateConversationHistory('assistant', action.content);
    
    // Update state based on action result
    this.updateStateFromActionResult(action, result);
    
    // Get current decision criteria
    const criteria = this.getCurrentDecisionCriteria();
    
    // Check if phase transition is needed
    if (this.phaseEngine.shouldTransition(this.state, criteria)) {
      this.executePhaseTransition(criteria);
    }
    
    // Generate next action
    const actions = this.decisionEngine.generateActions(this.state, criteria);
    const nextAction = this.decisionEngine.selectBestAction(actions);
    
    // Persist state to database
    await this.persistState();
    
    return nextAction || {
      type: 'message',
      content: 'What would you like to learn next?',
      confidence: 0.7,
    };
  }

  public getState(): AgentState {
    return { ...this.state };
  }

  public getLearningPlan(): LearningPlan | null {
    return this.learningPlan;
  }

  private async createLearningPlan(): Promise<LearningPlan> {
    const plan = LearningPlanSchema.parse({
      id: `plan_${this.state.sessionId}_${Date.now()}`,
      sessionId: this.state.sessionId,
      topic: this.state.currentTopic,
      objectives: this.state.learningGoals,
      phases: [
        {
          phase: 'planning',
          description: 'Understand learning goals and create plan',
          estimatedDuration: 5,
          learningGoals: ['Identify learning objectives', 'Assess current knowledge'],
          successCriteria: ['Clear learning goals established', 'Starting point identified'],
        },
        {
          phase: 'assessment',
          description: 'Evaluate current knowledge level',
          estimatedDuration: 10,
          learningGoals: ['Assess understanding level', 'Identify knowledge gaps'],
          successCriteria: ['Current level determined', 'Areas for improvement identified'],
        },
        {
          phase: 'explanation',
          description: 'Teach core concepts and theories',
          estimatedDuration: 20,
          learningGoals: ['Understand key concepts', 'Grasp fundamental principles'],
          successCriteria: ['Concepts explained clearly', 'User demonstrates understanding'],
        },
        {
          phase: 'practice',
          description: 'Apply knowledge through exercises',
          estimatedDuration: 15,
          learningGoals: ['Apply concepts practically', 'Develop problem-solving skills'],
          successCriteria: ['Exercises completed', 'Practical understanding demonstrated'],
        },
        {
          phase: 'qa',
          description: 'Address questions and clarify doubts',
          estimatedDuration: 10,
          learningGoals: ['Resolve confusion', 'Deepen understanding'],
          successCriteria: ['Questions answered', 'Concepts clarified'],
        },
        {
          phase: 'feedback',
          description: 'Provide performance feedback',
          estimatedDuration: 5,
          learningGoals: ['Understand performance', 'Identify improvement areas'],
          successCriteria: ['Feedback provided', 'Improvement areas identified'],
        },
        {
          phase: 'adaptation',
          description: 'Adjust approach based on performance',
          estimatedDuration: 5,
          learningGoals: ['Adapt learning strategy', 'Optimize approach'],
          successCriteria: ['Approach adjusted', 'Strategy optimized'],
        },
        {
          phase: 'reflection',
          description: 'Review progress and consolidate learning',
          estimatedDuration: 5,
          learningGoals: ['Review progress', 'Consolidate knowledge'],
          successCriteria: ['Progress reviewed', 'Learning consolidated'],
        },
        {
          phase: 'completion',
          description: 'Summarize learning and provide next steps',
          estimatedDuration: 5,
          learningGoals: ['Summarize achievements', 'Plan next steps'],
          successCriteria: ['Learning summarized', 'Next steps identified'],
        },
      ],
      adaptivePath: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Store learning plan in database
    try {
      await db.learningSession.create({
        data: {
          userId: 'temp-user-id', // TODO: Replace with actual user ID
          currentPhase: this.state.currentPhase,
          adaptiveDifficulty: this.state.difficulty,
        },
      });
    } catch (error) {
      console.error('Error storing learning session:', error);
    }

    return plan;
  }

  private executePhaseTransition(criteria: DecisionCriteria): PhaseTransition {
    const nextPhase = this.phaseEngine.getNextPhase(this.state, criteria);
    
    if (!nextPhase) {
      throw new Error('No valid phase transition found');
    }

    const transition: PhaseTransition = {
      fromPhase: this.state.currentPhase,
      toPhase: nextPhase,
      reason: `Transitioning based on ${criteria.performanceTrend} performance and ${criteria.userUnderstanding} understanding`,
      confidence: 0.8,
      triggers: ['performance_based_transition'],
      timestamp: new Date(),
    };

    // Update state
    this.state.previousPhase = this.state.currentPhase;
    this.state.currentPhase = nextPhase;
    this.state.phaseHistory.push(nextPhase);
    this.state.metadata.phaseTransitions++;
    this.state.metadata.lastActivity = new Date();

    // Update database
    this.updateDatabasePhase(nextPhase);

    return transition;
  }

  private updateStateFromUserResponse(userMessage: string, criteria: DecisionCriteria): Partial<AgentState> {
    const stateUpdate: Partial<AgentState> = {
      userPerformance: {
        ...this.state.userPerformance,
        understandingLevel: criteria.userUnderstanding,
        engagementScore: criteria.userEngagement,
        lastResponseTime: Date.now(),
      },
      metadata: {
        ...this.state.metadata,
        lastActivity: new Date(),
      },
    };

    // Update total questions and correct answers based on quiz responses
    if (userMessage.toLowerCase().includes('quiz') || userMessage.includes('?')) {
      stateUpdate.userPerformance!.totalQuestions++;
      
      // Simple heuristic for correct answers (would be more sophisticated in practice)
      if (userMessage.toLowerCase().includes('correct') || userMessage.toLowerCase().includes('right')) {
        stateUpdate.userPerformance!.correctAnswers++;
      }
    }

    // Apply state update
    this.state = { ...this.state, ...stateUpdate };

    return stateUpdate;
  }

  private updateStateFromActionResult(action: AgentAction, result: any): void {
    // Update state based on action results
    if (action.type === 'quiz' && result) {
      this.state.userPerformance.totalQuestions += result.totalQuestions || 0;
      this.state.userPerformance.correctAnswers += result.correctAnswers || 0;
    }

    if (action.type === 'adaptation') {
      this.state.metadata.adaptations++;
      if (action.metadata?.newDifficulty &&
          ['beginner', 'intermediate', 'advanced'].includes(action.metadata.newDifficulty as string)) {
        this.state.difficulty = action.metadata.newDifficulty as 'beginner' | 'intermediate' | 'advanced';
      }
    }

    this.state.metadata.lastActivity = new Date();
  }

  private updateConversationHistory(role: 'user' | 'assistant', content: string): void {
    this.state.context.conversationHistory.push({
      role,
      content,
      timestamp: new Date(),
    });

    // Keep only last 50 messages to prevent memory issues
    if (this.state.context.conversationHistory.length > 50) {
      this.state.context.conversationHistory = this.state.context.conversationHistory.slice(-50);
    }
  }

  private getCurrentDecisionCriteria(): DecisionCriteria {
    return {
      userUnderstanding: this.state.userPerformance.understandingLevel,
      userEngagement: this.state.userPerformance.engagementScore,
      topicComplexity: 0.5, // Default complexity
      timeSpent: (Date.now() - this.state.metadata.startTime.getTime()) / 60000,
      questionFrequency: this.calculateQuestionFrequency(),
      performanceTrend: 'stable', // Default trend
      userFeedback: [],
    };
  }

  private calculateQuestionFrequency(): number {
    const recentMessages = this.state.context.conversationHistory.slice(-10);
    const questions = recentMessages.filter(msg => 
      msg.role === 'user' && msg.content.includes('?')
    ).length;
    
    return questions / Math.max(recentMessages.length, 1);
  }

  private calculateResponseTime(): number {
    if (this.state.context.conversationHistory.length === 0) {
      return 0;
    }

    const lastMessage = this.state.context.conversationHistory[this.state.context.conversationHistory.length - 1];
    return Date.now() - lastMessage.timestamp.getTime();
  }

  private async persistState(): Promise<void> {
    try {
      // Update learning session in database
      await db.learningSession.updateMany({
        where: { id: this.state.sessionId },
        data: {
          currentPhase: this.state.currentPhase,
          adaptiveDifficulty: this.state.difficulty,
          progress: this.calculateOverallProgress(),
          updatedAt: new Date(),
        },
      });

      // Store user interaction
      if (this.state.context.conversationHistory.length > 0) {
        const lastMessage = this.state.context.conversationHistory[this.state.context.conversationHistory.length - 1];
        if (lastMessage.role === 'user') {
          await db.userInteraction.create({
            data: {
              sessionId: this.state.sessionId,
              message: lastMessage.content,
              responseType: 'proceed', // Default response type
              timestamp: lastMessage.timestamp,
            },
          });
        }
      }
    } catch (error) {
      console.error('Error persisting agent state:', error);
    }
  }

  private async updateDatabasePhase(newPhase: AgentPhaseType): Promise<void> {
    try {
      await db.learningSession.updateMany({
        where: { id: this.state.sessionId },
        data: {
          currentPhase: newPhase,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error updating database phase:', error);
    }
  }

  private calculateOverallProgress(): number {
    const phaseProgress = this.state.phaseHistory.length / 9; // 9 total phases
    const understandingProgress = this.state.userPerformance.understandingLevel;
    const engagementProgress = this.state.userPerformance.engagementScore;
    
    return (phaseProgress + understandingProgress + engagementProgress) / 3;
  }

  public async getProgressReport(): Promise<{
    overallProgress: number;
    phaseProgress: { phase: AgentPhaseType; completed: boolean; duration?: number }[];
    performanceMetrics: {
      understandingLevel: number;
      engagementScore: number;
      accuracy: number;
      timeSpent: number;
    };
    recommendations: string[];
  }> {
    const timeSpent = (Date.now() - this.state.metadata.startTime.getTime()) / 60000;
    const accuracy = this.state.userPerformance.correctAnswers / Math.max(this.state.userPerformance.totalQuestions, 1);

    const phaseProgress = this.state.phaseHistory.map((phase, index) => ({
      phase,
      completed: index < this.state.phaseHistory.length - 1,
      duration: index > 0 ? 5 : undefined, // Simplified duration calculation
    }));

    const recommendations = this.generateRecommendations();

    return {
      overallProgress: this.calculateOverallProgress(),
      phaseProgress,
      performanceMetrics: {
        understandingLevel: this.state.userPerformance.understandingLevel,
        engagementScore: this.state.userPerformance.engagementScore,
        accuracy,
        timeSpent,
      },
      recommendations,
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.state.userPerformance.understandingLevel < 0.5) {
      recommendations.push('Focus on reviewing fundamental concepts');
    }
    
    if (this.state.userPerformance.engagementScore < 0.5) {
      recommendations.push('Try more interactive learning activities');
    }
    
    if (this.state.userPerformance.correctAnswers / Math.max(this.state.userPerformance.totalQuestions, 1) < 0.6) {
      recommendations.push('Practice with more exercises to improve accuracy');
    }
    
    if (this.state.currentPhase === 'completion') {
      recommendations.push('Consider moving to more advanced topics');
    }

    return recommendations;
  }
}