import ai from '@/lib/genkit';
import { gemini15Flash } from '@genkit-ai/googleai';
import { z } from 'zod';
import { Curriculum, CurriculumStep, LearningPhase, StepType } from './types';

// Debug logging to check Genkit initialization
console.log('🔍 [DEBUG] curriculum-generator.ts: Loading...');
console.log('🔍 [DEBUG] Genkit instance:', typeof ai);
console.log('🔍 [DEBUG] Genkit GoogleAI module:', typeof gemini15Flash);

// Define the curriculum generation flow
export const generateCurriculumFlow = ai.defineFlow(
  {
    name: 'generateCurriculumFlow',
    inputSchema: z.object({
      topic: z.string(),
      subject: z.string(),
      difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
      userGoals: z.array(z.string()).optional(),
      timeAvailable: z.string().optional(),
      priorKnowledge: z.string().optional(),
    }),
    outputSchema: z.object({
      curriculum: z.any(),
      success: z.boolean(),
      message: z.string(),
    }),
  },
  async (input) => {
    const { topic, subject, difficulty, userGoals, timeAvailable, priorKnowledge } = input;

    const systemPrompt = `You are an expert curriculum designer and educational AI. Your task is to create a comprehensive, step-by-step learning curriculum for the given topic.

Topic: ${topic}
Subject: ${subject}
Difficulty Level: ${difficulty}
User Goals: ${userGoals?.join(', ') || 'Not specified'}
Time Available: ${timeAvailable || 'Not specified'}
Prior Knowledge: ${priorKnowledge || 'None specified'}

Instructions:
1. Break down the topic into logical, sequential learning steps
2. Each step should build upon previous knowledge
3. Include different types of learning activities (introduction, concepts, examples, analogies, practice, quizzes)
4. Estimate time for each step realistically
5. Ensure the curriculum flows naturally from basics to advanced concepts
6. Include clear learning objectives
7. Identify prerequisites and dependencies between steps

Curriculum Structure:
- Start with an introduction to engage the learner
- Follow with core concepts in logical order
- Include examples and analogies for better understanding
- Add practice exercises and quizzes for reinforcement
- End with a summary and next steps

Response Format:
Return a JSON object with the following structure:
{
  "id": "unique-curriculum-id",
  "topic": "${topic}",
  "subject": "${subject}",
  "description": "Brief description of the curriculum",
  "difficulty": "${difficulty}",
  "estimatedTotalTime": "Total estimated time (e.g., '2 hours')",
  "learningObjectives": ["Objective 1", "Objective 2", "Objective 3"],
  "prerequisites": ["Prerequisite 1", "Prerequisite 2"],
  "steps": [
    {
      "id": "step-1",
      "title": "Step Title",
      "description": "Brief description of the step",
      "type": "introduction|concept|example|analogy|practice|quiz|summary|advanced",
      "content": "Detailed content for this step",
      "difficulty": "beginner|intermediate|advanced",
      "estimatedTime": "Time estimate (e.g., '15 minutes')",
      "prerequisites": [],
      "dependencies": [],
      "isCompleted": false,
      "resources": []
    }
  ]
}

Ensure the curriculum is comprehensive, engaging, and pedagogically sound.`;

    try {
      const response = await ai.generate({
        model: gemini15Flash,
        messages: [
          {
            role: 'system' as const,
            content: [{ text: systemPrompt }],
          },
          {
            role: 'user' as const,
            content: [{ text: `Please generate a comprehensive curriculum for learning about ${topic} in ${subject}.` }],
          },
        ],
        config: {
          temperature: 0.7,
          maxOutputTokens: 4000,
        },
      });

      const responseText = response.text;
      
      // Extract JSON from the response
      let curriculumData;
      try {
        // Find JSON in the response (it might be wrapped in markdown code blocks)
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          curriculumData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (error) {
        console.error('Failed to parse curriculum JSON:', error);
        throw new Error('Failed to generate curriculum structure');
      }

      // Validate and enhance the curriculum
      const enhancedCurriculum = enhanceCurriculum(curriculumData, topic, subject);

      return {
        curriculum: enhancedCurriculum,
        success: true,
        message: 'Curriculum generated successfully',
      };

    } catch (error) {
      console.error('Curriculum generation error:', error);
      return {
        curriculum: null,
        success: false,
        message: `Failed to generate curriculum: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
);

// Enhance curriculum with additional metadata and validation
function enhanceCurriculum(data: any, topic: string, subject: string): Curriculum {
  const curriculum: Curriculum = {
    id: data.id || `curriculum-${Date.now()}`,
    topic: data.topic || topic,
    subject: data.subject || subject,
    description: data.description || `Comprehensive curriculum for learning ${topic}`,
    difficulty: data.difficulty || 'intermediate',
    estimatedTotalTime: data.estimatedTotalTime || '2 hours',
    learningObjectives: data.learningObjectives || [],
    prerequisites: data.prerequisites || [],
    steps: data.steps?.map((step: any, index: number) => ({
      id: step.id || `step-${index + 1}`,
      title: step.title || `Step ${index + 1}`,
      description: step.description || '',
      type: step.type || StepType.CONCEPT,
      content: step.content || '',
      difficulty: step.difficulty || 'intermediate',
      estimatedTime: step.estimatedTime || '15 minutes',
      prerequisites: step.prerequisites || [],
      dependencies: step.dependencies || [],
      isCompleted: false,
      resources: step.resources || [],
    })) || [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Validate curriculum structure
  if (!curriculum.steps || curriculum.steps.length === 0) {
    throw new Error('Generated curriculum must have at least one step');
  }

  // Ensure first step is an introduction
  if (curriculum.steps[0].type !== StepType.INTRODUCTION) {
    curriculum.steps.unshift({
      id: 'step-intro',
      title: `Introduction to ${topic}`,
      description: `Get started with learning about ${topic}`,
      type: StepType.INTRODUCTION,
      content: `Welcome to your learning journey about ${topic}! In this introduction, we'll explore what ${topic} is, why it's important, and what you'll learn in this curriculum.`,
      difficulty: 'beginner',
      estimatedTime: '10 minutes',
      prerequisites: [],
      dependencies: [],
      isCompleted: false,
      resources: [],
    });
  }

  // Ensure last step is a summary
  if (curriculum.steps[curriculum.steps.length - 1].type !== StepType.SUMMARY) {
    curriculum.steps.push({
      id: 'step-summary',
      title: `Summary and Next Steps`,
      description: `Review what you've learned about ${topic}`,
      type: StepType.SUMMARY,
      content: `Congratulations on completing your journey through ${topic}! Let's summarize what you've learned and explore where you can go from here.`,
      difficulty: 'intermediate',
      estimatedTime: '15 minutes',
      prerequisites: [],
      dependencies: [],
      isCompleted: false,
      resources: [],
    });
  }

  return curriculum;
}

// Export flow runner
export const generateCurriculum = (input: any) => generateCurriculumFlow(input);

// Utility function to get next step in curriculum
export function getNextStep(curriculum: Curriculum, currentStepIndex: number): CurriculumStep | null {
  if (currentStepIndex < curriculum.steps.length - 1) {
    return curriculum.steps[currentStepIndex + 1];
  }
  return null;
}

// Utility function to check if step can be accessed
export function canAccessStep(curriculum: Curriculum, stepIndex: number, completedSteps: string[]): boolean {
  const step = curriculum.steps[stepIndex];
  if (!step) return false;

  // Check if all prerequisites are completed
  return step.prerequisites.every(prereq => completedSteps.includes(prereq));
}

// Utility function to calculate curriculum progress
export function calculateProgress(curriculum: Curriculum, completedSteps: string[]): number {
  if (curriculum.steps.length === 0) return 0;
  return Math.round((completedSteps.length / curriculum.steps.length) * 100);
}