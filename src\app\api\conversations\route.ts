import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const conversationId = searchParams.get('conversationId');

    if (conversationId) {
      // Get specific conversation with messages
      const conversation = await db.conversation.findUnique({
        where: { id: conversationId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' }
          },
          session: true
        }
      });

      if (!conversation) {
        return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
      }

      return NextResponse.json({ conversation });
    }

    if (sessionId) {
      // Get conversation for a specific session
      const conversation = await db.conversation.findFirst({
        where: { sessionId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' }
          },
          session: true
        }
      });

      return NextResponse.json({ conversation });
    }

    return NextResponse.json({ error: 'Session ID or Conversation ID is required' }, { status: 400 });

  } catch (error) {
    console.error('Get conversation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}