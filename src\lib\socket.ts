import { Server } from 'socket.io';

interface ChatMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  userId: string;
}

interface StudySession {
  id: string;
  title: string;
  isActive: boolean;
  userId: string;
}

export const setupSocket = (io: Server) => {
  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);
    
    // Join a study session room
    socket.on('join-session', (sessionId: string) => {
      socket.join(`session-${sessionId}`);
      console.log(`Client ${socket.id} joined session ${sessionId}`);
    });

    // Leave a study session room
    socket.on('leave-session', (sessionId: string) => {
      socket.leave(`session-${sessionId}`);
      console.log(`Client ${socket.id} left session ${sessionId}`);
    });

    // Handle real-time chat messages
    socket.on('chat-message', async (data: {
      message: string;
      conversationId: string;
      sessionId: string;
      userId: string;
    }) => {
      try {
        // Broadcast message to all clients in the session
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          conversationId: data.conversationId,
          role: 'user',
          content: data.message,
          timestamp: new Date(),
          userId: data.userId,
        };

        io.to(`session-${data.sessionId}`).emit('message', userMessage);

        // Simulate AI processing (in real implementation, this would call the AI service)
        setTimeout(() => {
          const aiMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            conversationId: data.conversationId,
            role: 'assistant',
            content: 'This is a real-time AI response.',
            timestamp: new Date(),
            userId: 'ai-assistant',
          };

          io.to(`session-${data.sessionId}`).emit('message', aiMessage);
        }, 1000);

      } catch (error) {
        console.error('Error handling chat message:', error);
        socket.emit('error', { message: 'Failed to process message' });
      }
    });

    // Handle study session updates
    socket.on('session-update', (session: StudySession) => {
      io.to(`session-${session.id}`).emit('session-updated', session);
    });

    // Handle typing indicators
    socket.on('typing', (data: { sessionId: string; userId: string; isTyping: boolean }) => {
      socket.to(`session-${data.sessionId}`).emit('user-typing', {
        userId: data.userId,
        isTyping: data.isTyping,
      });
    });

    // Handle session activity
    socket.on('session-activity', (data: { sessionId: string; activity: string }) => {
      io.to(`session-${data.sessionId}`).emit('activity-log', {
        activity: data.activity,
        timestamp: new Date().toISOString(),
      });
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
    });

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to StudyAI real-time server',
      timestamp: new Date().toISOString(),
    });
  });
};