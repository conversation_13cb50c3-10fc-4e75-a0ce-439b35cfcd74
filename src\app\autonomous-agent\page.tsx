'use client';

import { useState, useEffect } from 'react';
import { AutonomousLearningAgent } from '@/components/autonomous-learning-agent';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON>, ArrowLeft, Sparkles } from 'lucide-react';
import Link from 'next/link';

export default function AutonomousAgentPage() {
  const [sessionId, setSessionId] = useState('');
  const [showAgent, setShowAgent] = useState(false);

  useEffect(() => {
    // Generate a unique session ID for this page visit
    setSessionId(`agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  }, []);

  const handleSessionEnd = () => {
    setShowAgent(false);
    // Generate a new session ID for next time
    setSessionId(`agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  };

  if (!showAgent) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted flex items-center justify-center p-4">
        <div className="max-w-4xl w-full">
          <div className="text-center mb-8">
            <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
              <Brain className="h-10 w-10 text-primary" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl">
              Multi-Phase Autonomous Learning Agent
            </h1>
            <p className="mt-4 text-xl text-muted-foreground max-w-2xl mx-auto">
              Experience the future of AI-powered education with our autonomous learning agent that 
              adapts to your needs, manages learning phases, and guides you through personalized learning journeys.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <Card className="border-none shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  Intelligent Planning
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  The agent automatically creates personalized learning plans based on your goals and current knowledge level.
                </p>
              </CardContent>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  Adaptive Phases
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Seamlessly transitions between 9 different learning phases based on your performance and engagement.
                </p>
              </CardContent>
            </Card>

            <Card className="border-none shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-green-600" />
                  Real-time Adaptation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Continuously analyzes your responses and adapts difficulty, pace, and teaching style to optimize learning.
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="border-none shadow-lg">
            <CardHeader className="text-center pb-6">
              <CardTitle className="flex items-center justify-center gap-2 text-2xl">
                <Sparkles className="h-6 w-6 text-primary" />
                Ready to Begin Your Autonomous Learning Journey?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-lg text-muted-foreground mb-6">
                  Our AI agent will guide you through a complete learning experience with:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left max-w-3xl mx-auto">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">📋 Planning Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Understands your goals and creates a customized learning path
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">🎯 Assessment Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Evaluates your current knowledge and identifies learning gaps
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">📚 Explanation Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Provides tailored explanations based on your learning style
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">💪 Practice Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Offers interactive exercises to reinforce learning
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">❓ Q&A Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Answers questions and clarifies doubts in real-time
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-primary">📊 Feedback Phase</h4>
                    <p className="text-sm text-muted-foreground">
                      Provides detailed performance feedback and insights
                    </p>
                  </div>
                </div>
              </div>

              <div className="text-center space-y-4">
                <Button 
                  size="lg" 
                  className="px-8 py-3 text-lg"
                  onClick={() => setShowAgent(true)}
                >
                  <Brain className="mr-2 h-5 w-5" />
                  Start Autonomous Learning Session
                </Button>
                
                <div className="flex items-center justify-center gap-4 text-sm">
                  <Link href="/" className="text-primary hover:underline flex items-center gap-1">
                    <ArrowLeft className="h-4 w-4" />
                    Back to Traditional Chat
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="border-b bg-card">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Brain className="h-6 w-6 text-primary" />
            <h1 className="text-xl font-bold">Autonomous Learning Agent</h1>
          </div>
          <Button 
            variant="outline" 
            onClick={() => setShowAgent(false)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Exit Session
          </Button>
        </div>
      </div>
      
      <AutonomousLearningAgent 
        sessionId={sessionId} 
        onSessionEnd={handleSessionEnd}
      />
    </div>
  );
}