'use client';

import { useState, useCallback, useEffect } from 'react';

interface AgentAction {
  type: 'message' | 'quiz' | 'explanation' | 'practice' | 'feedback' | 'adaptation';
  content: string;
  metadata?: Record<string, unknown>;
  expectedResponse?: string;
  confidence: number;
}

interface AgentState {
  sessionId: string;
  currentPhase: string;
  previousPhase?: string;
  phaseHistory: string[];
  learningGoals: string[];
  currentTopic: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  userPerformance: {
    correctAnswers: number;
    totalQuestions: number;
    understandingLevel: number;
    engagementScore: number;
    lastResponseTime?: number;
  };
  context: {
    conversationHistory: Array<{
      role: 'user' | 'assistant';
      content: string;
      timestamp: string;
    }>;
    currentStep: number;
    totalSteps: number;
    learningObjectives: string[];
    prerequisites: string[];
  };
  metadata: {
    startTime: string;
    lastActivity: string;
    phaseTransitions: number;
    adaptations: number;
  };
}

interface PhaseTransition {
  fromPhase: string;
  toPhase: string;
  reason: string;
  confidence: number;
  triggers: string[];
  timestamp: string;
}

interface UseAutonomousAgentReturn {
  state: AgentState | null;
  isLoading: boolean;
  error: string | null;
  initializeAgent: (topic: string, goals?: string[]) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  reportActionResult: (actionType: string, result: any) => Promise<void>;
  getProgress: () => Promise<any>;
  getState: () => Promise<void>;
  endSession: () => Promise<void>;
  currentAction: AgentAction | null;
  phaseTransition: PhaseTransition | null;
}

export function useAutonomousAgent(sessionId: string): UseAutonomousAgentReturn {
  const [state, setState] = useState<AgentState | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAction, setCurrentAction] = useState<AgentAction | null>(null);
  const [phaseTransition, setPhaseTransition] = useState<PhaseTransition | null>(null);

  const initializeAgent = useCallback(async (topic: string, goals: string[] = []) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'initialize',
          sessionId,
          topic,
          goals,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to initialize agent');
      }

      const data = await response.json();
      setState(data.state);
      setCurrentAction(data.action);
      setPhaseTransition(data.phaseTransition || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const sendMessage = useCallback(async (message: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'message',
          sessionId,
          message,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      setState(data.state);
      setCurrentAction(data.action);
      setPhaseTransition(data.phaseTransition || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const reportActionResult = useCallback(async (actionType: string, result: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'action_result',
          sessionId,
          actionType,
          result,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to report action result');
      }

      const data = await response.json();
      setState(data.state);
      setCurrentAction(data.action);
      setPhaseTransition(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const getProgress = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_progress',
          sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get progress');
      }

      const data = await response.json();
      return data.progress;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const getState = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/agent?sessionId=${sessionId}`, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Failed to get state');
      }

      const data = await response.json();
      setState(data.state);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const endSession = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'end_session',
          sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to end session');
      }

      setState(null);
      setCurrentAction(null);
      setPhaseTransition(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  // Auto-refresh state every 30 seconds
  useEffect(() => {
    if (state) {
      const interval = setInterval(() => {
        getState();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [state, getState]);

  return {
    state,
    isLoading,
    error,
    initializeAgent,
    sendMessage,
    reportActionResult,
    getProgress,
    getState,
    endSession,
    currentAction,
    phaseTransition,
  };
}