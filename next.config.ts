import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  allowedDevOrigins: ['127.0.0.1', 'localhost'],
  typescript: {
    ignoreBuildErrors: true,
  },
  // 禁用 Next.js 热重载，由 nodemon 处理重编译
  reactStrictMode: false,
  webpack: (config, { dev }) => {
    if (dev) {
      // 禁用 webpack 的热模块替换
      config.watchOptions = {
        ignored: ['**/*'], // 忽略所有文件变化
      };
    }

    // Suppress warnings about dotprompt and handlebars dependencies
    config.ignoreWarnings = [
      /Module not found: Can't resolve 'handlebars'/,
      /Module not found: Can't resolve 'dotprompt'/,
      /Critical dependency: the request of a dependency is an expression/,
    ];

    return config;
  },

};

export default nextConfig;
