"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import LearningInterface from "@/components/ai-agent/learning-interface";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, BookOpen } from "lucide-react";
import Link from "next/link";

export default function AIAgentPage() {
  const params = useParams();
  const studySessionId = params.id as string;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleSessionComplete = () => {
    // Handle session completion
    console.log("Learning session completed!");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading AI Learning Agent...</p>
        </div>
      </div>
    );
  }

  if (!studySessionId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <BookOpen className="h-12 w-12 text-primary mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Study Session Selected</h2>
            <p className="text-muted-foreground mb-4">
              Please select a study session to start learning with the AI Agent.
            </p>
            <Link href="/app">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Study Sessions
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background">
      <div className="h-full">
        <LearningInterface 
          studySessionId={studySessionId}
          onSessionComplete={handleSessionComplete}
        />
      </div>
    </div>
  );
}