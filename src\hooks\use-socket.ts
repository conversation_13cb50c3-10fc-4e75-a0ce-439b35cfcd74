'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseSocketProps {
  sessionId?: string;
  userId?: string;
}

export const useSocket = ({ sessionId, userId = 'temp-user' }: UseSocketProps = {}) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [activityLog, setActivityLog] = useState<Array<{ activity: string; timestamp: string }>>([]);

  useEffect(() => {
    // Initialize socket connection
    socketRef.current = io(process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : '', {
      transports: ['websocket', 'polling'],
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      setIsConnected(true);
      console.log('Connected to socket server');
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      console.log('Disconnected from socket server');
    });

    socket.on('connected', (data) => {
      console.log('Socket connection confirmed:', data.message);
    });

    // Join session if provided
    if (sessionId) {
      socket.emit('join-session', sessionId);
    }

    // Message handling
    socket.on('message', (message) => {
      // This will be handled by the component that uses the hook
      // We'll use a custom event or callback for this
    });

    // Typing indicators
    socket.on('user-typing', (data) => {
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        if (data.isTyping) {
          newSet.add(data.userId);
        } else {
          newSet.delete(data.userId);
        }
        return newSet;
      });
    });

    // Activity log
    socket.on('activity-log', (data) => {
      setActivityLog(prev => [...prev.slice(-9), data]); // Keep last 10 activities
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    // Cleanup on unmount
    return () => {
      if (sessionId) {
        socket.emit('leave-session', sessionId);
      }
      socket.disconnect();
    };
  }, [sessionId, userId]);

  // Join a new session
  const joinSession = (newSessionId: string) => {
    if (socketRef.current) {
      if (sessionId) {
        socketRef.current.emit('leave-session', sessionId);
      }
      socketRef.current.emit('join-session', newSessionId);
    }
  };

  // Send a chat message
  const sendMessage = (data: {
    message: string;
    conversationId: string;
    sessionId: string;
    userId: string;
  }) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('chat-message', data);
    }
  };

  // Send typing indicator
  const sendTyping = (isTyping: boolean) => {
    if (socketRef.current && isConnected && sessionId) {
      socketRef.current.emit('typing', {
        sessionId,
        userId,
        isTyping,
      });
    }
  };

  // Send session activity
  const sendActivity = (activity: string) => {
    if (socketRef.current && isConnected && sessionId) {
      socketRef.current.emit('session-activity', {
        sessionId,
        activity,
      });
    }
  };

  // Update session
  const updateSession = (session: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('session-update', session);
    }
  };

  // Listen for messages (callback version)
  const onMessage = (callback: (message: any) => void) => {
    if (socketRef.current) {
      socketRef.current.on('message', callback);
    }
  };

  // Remove message listener
  const offMessage = (callback: (message: any) => void) => {
    if (socketRef.current) {
      socketRef.current.off('message', callback);
    }
  };

  return {
    isConnected,
    typingUsers,
    activityLog,
    joinSession,
    sendMessage,
    sendTyping,
    sendActivity,
    updateSession,
    onMessage,
    offMessage,
    socket: socketRef.current,
  };
};