"use client";

import { useUser } from "@clerk/nextjs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { User, Settings as SettingsIcon, Bell, BookOpen, Brain, Shield } from "lucide-react";
import { useState } from "react";

export default function SettingsPage() {
  const { user } = useUser();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [studyReminders, setStudyReminders] = useState(true);
  const [aiResponseStyle, setAiResponseStyle] = useState("balanced");

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="flex items-center gap-4 mb-8">
        <div className="p-2 bg-primary/10 rounded-lg">
          <SettingsIcon className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">Manage your account and preferences</p>
        </div>
      </div>

      {/* Profile Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile Information
          </CardTitle>
          <CardDescription>
            Update your personal information and account details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input 
                id="firstName" 
                defaultValue={user?.firstName || ""}
                placeholder="Enter your first name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input 
                id="lastName" 
                defaultValue={user?.lastName || ""}
                placeholder="Enter your last name"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input 
              id="email" 
              type="email"
              defaultValue={user?.emailAddresses[0]?.emailAddress || ""}
              placeholder="Enter your email"
              disabled
            />
            <p className="text-sm text-muted-foreground">
              Email changes must be made through your Clerk account settings
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Textarea 
              id="bio" 
              placeholder="Tell us about yourself and your learning goals"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="studyLevel">Study Level</Label>
            <Select defaultValue="undergraduate">
              <SelectTrigger>
                <SelectValue placeholder="Select your study level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="high-school">High School</SelectItem>
                <SelectItem value="undergraduate">Undergraduate</SelectItem>
                <SelectItem value="graduate">Graduate</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button>Save Profile Changes</Button>
        </CardContent>
      </Card>

      {/* Learning Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Learning Preferences
          </CardTitle>
          <CardDescription>
            Customize how you interact with the AI study assistant
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="aiResponseStyle">AI Response Style</Label>
            <Select value={aiResponseStyle} onValueChange={setAiResponseStyle}>
              <SelectTrigger>
                <SelectValue placeholder="Choose AI response style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="concise">Concise - Quick and to the point</SelectItem>
                <SelectItem value="balanced">Balanced - Detailed but focused</SelectItem>
                <SelectItem value="comprehensive">Comprehensive - In-depth explanations</SelectItem>
                <SelectItem value="conversational">Conversational - Friendly and engaging</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Choose how detailed you want AI responses to be
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="preferredSubjects">Preferred Subjects</Label>
            <Input 
              id="preferredSubjects" 
              placeholder="e.g., Mathematics, Physics, Computer Science"
            />
            <p className="text-sm text-muted-foreground">
              Enter subjects you're most interested in studying
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="learningGoal">Primary Learning Goal</Label>
            <Textarea 
              id="learningGoal" 
              placeholder="What are you trying to achieve with your studies?"
              rows={2}
            />
          </div>

          <Button>Save Learning Preferences</Button>
        </CardContent>
      </Card>

      {/* AI Agent Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Agent Settings
          </CardTitle>
          <CardDescription>
            Configure the autonomous AI agent behavior
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Autonomous Mode</Label>
              <p className="text-sm text-muted-foreground">
                Allow AI agent to guide your learning automatically
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Adaptive Difficulty</Label>
              <p className="text-sm text-muted-foreground">
                Automatically adjust content difficulty based on your progress
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Progress Tracking</Label>
              <p className="text-sm text-muted-foreground">
                Allow AI to track and analyze your learning patterns
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label htmlFor="sessionDuration">Preferred Session Duration</Label>
            <Select defaultValue="30">
              <SelectTrigger>
                <SelectValue placeholder="Select session duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15">15 minutes</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="45">45 minutes</SelectItem>
                <SelectItem value="60">60 minutes</SelectItem>
                <SelectItem value="90">90 minutes</SelectItem>
                <SelectItem value="120">120 minutes</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button>Save AI Agent Settings</Button>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </CardTitle>
          <CardDescription>
            Manage how and when you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive email updates about your progress
              </p>
            </div>
            <Switch 
              checked={notificationsEnabled}
              onCheckedChange={setNotificationsEnabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Study Reminders</Label>
              <p className="text-sm text-muted-foreground">
                Get reminded to start your study sessions
              </p>
            </div>
            <Switch 
              checked={studyReminders}
              onCheckedChange={setStudyReminders}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Dark Mode</Label>
              <p className="text-sm text-muted-foreground">
                Use dark theme for better night-time studying
              </p>
            </div>
            <Switch 
              checked={darkMode}
              onCheckedChange={setDarkMode}
            />
          </div>

          <Button>Save Notification Settings</Button>
        </CardContent>
      </Card>

      {/* Account Security */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Account Security
          </CardTitle>
          <CardDescription>
            Manage your account security and privacy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Two-Factor Authentication</h4>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Badge variant="outline">Not Enabled</Badge>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Account Connected</h4>
              <p className="text-sm text-muted-foreground">
                Your account is securely connected via Clerk
              </p>
            </div>
            <Badge variant="secondary">Secure</Badge>
          </div>

          <div className="pt-4">
            <Button variant="outline" className="w-full">
              Manage Security Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}