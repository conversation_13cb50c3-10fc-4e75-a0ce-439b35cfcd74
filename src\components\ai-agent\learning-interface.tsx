"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  MessageSquare, 
  BookOpen, 
  Target, 
  Clock, 
  Send, 
  CheckCircle, 
  Circle,
  ChevronRight,
  Lightbulb,
  HelpCircle,
  Play,
  Award
} from "lucide-react";
import { LearningPhase, Curriculum, LearningSession } from "@/lib/ai-agent/types";

// Extended type that includes the curriculum relation from Prisma
interface LearningSessionWithCurriculum extends LearningSession {
  curriculum?: Curriculum | null;
}

interface LearningInterfaceProps {
  studySessionId?: string;
  onSessionComplete?: () => void;
}

interface AgentMessage {
  id: string;
  type: 'curriculum' | 'explanation' | 'question' | 'practice' | 'feedback' | 'guidance';
  content: string;
  phase: LearningPhase;
  timestamp: Date;
  metadata?: {
    suggestedActions?: string[];
    confidence?: number;
    nextStepHint?: string;
    learningTips?: string[];
  };
}

interface UserMessage {
  id: string;
  content: string;
  timestamp: Date;
}

export default function LearningInterface({ studySessionId, onSessionComplete }: LearningInterfaceProps) {
  const [messages, setMessages] = useState<(AgentMessage | UserMessage)[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [learningSession, setLearningSession] = useState<LearningSessionWithCurriculum | null>(null);
  const [suggestedActions, setSuggestedActions] = useState<string[]>([]);
  const [currentPhase, setCurrentPhase] = useState<LearningPhase>(LearningPhase.PLANNING);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize learning session
  useEffect(() => {
    if (studySessionId) {
      loadLearningSession();
    }
  }, [studySessionId]);

  // Scroll to bottom of messages
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadLearningSession = async () => {
    try {
      const response = await fetch(`/api/ai-agent?studySessionId=${studySessionId}`);
      if (response.ok) {
        const data = await response.json();
        setLearningSession(data.session);
        setCurrentPhase(data.session.currentPhase);
        
        // Load existing messages if any
        if (data.session.userResponses?.length > 0) {
          // Convert user responses to messages format
          const userMessages: UserMessage[] = data.session.userResponses.map((response: any) => ({
            id: response.id,
            content: response.response,
            timestamp: new Date(response.timestamp),
          }));
          
          setMessages(prev => [...userMessages, ...prev]);
        }
      }
    } catch (error) {
      console.error('Failed to load learning session:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: UserMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);
    setSuggestedActions([]);

    try {
      const response = await fetch('/api/ai-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          studySessionId,
          sessionId: learningSession?.id,
          context: {
            previousMessages: messages.slice(-10), // Send last 10 messages for context
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();
      
      // Update learning session
      if (data.session) {
        setLearningSession(data.session);
        setCurrentPhase(data.session.currentPhase);
      }

      // Add agent message
      const agentMessage: AgentMessage = {
        id: `agent-${Date.now()}`,
        type: data.agentMessage?.type || 'guidance',
        content: data.response,
        phase: data.phase,
        timestamp: new Date(),
        metadata: {
          suggestedActions: data.suggestedActions,
          confidence: data.confidence,
          nextStepHint: data.agentMessage?.metadata?.nextStepHint,
          learningTips: data.agentMessage?.metadata?.learningTips,
        },
      };

      setMessages(prev => [...prev, agentMessage]);
      setSuggestedActions(data.suggestedActions || []);

      // Check if session is completed
      if (data.session?.isCompleted) {
        onSessionComplete?.();
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: AgentMessage = {
        id: `error-${Date.now()}`,
        type: 'guidance',
        content: "I apologize, but I encountered an error. Please try again.",
        phase: LearningPhase.EXPLANATION,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestedAction = (action: string) => {
    setInputMessage(action);
    // Auto-send the suggested action
    setTimeout(() => handleSendMessage(), 100);
  };

  const getPhaseIcon = (phase: LearningPhase) => {
    switch (phase) {
      case LearningPhase.PLANNING:
        return <Target className="h-4 w-4" />;
      case LearningPhase.EXPLANATION:
        return <BookOpen className="h-4 w-4" />;
      case LearningPhase.QA:
        return <HelpCircle className="h-4 w-4" />;
      case LearningPhase.PRACTICE:
        return <Play className="h-4 w-4" />;
      case LearningPhase.ASSESSMENT:
        return <Award className="h-4 w-4" />;
      case LearningPhase.COMPLETION:
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getPhaseColor = (phase: LearningPhase) => {
    switch (phase) {
      case LearningPhase.PLANNING:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case LearningPhase.EXPLANATION:
        return "bg-green-100 text-green-800 border-green-200";
      case LearningPhase.QA:
        return "bg-purple-100 text-purple-800 border-purple-200";
      case LearningPhase.PRACTICE:
        return "bg-orange-100 text-orange-800 border-orange-200";
      case LearningPhase.ASSESSMENT:
        return "bg-red-100 text-red-800 border-red-200";
      case LearningPhase.COMPLETION:
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const renderCurriculumSteps = () => {
    if (!learningSession?.curriculum) return null;

    const curriculum = learningSession.curriculum;
    const currentStepIndex = learningSession.currentStepIndex;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Learning Path
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">{Math.round(learningSession.progress)}%</span>
            </div>
            <Progress value={learningSession.progress} className="mb-4" />
            
            <div className="space-y-2">
              {curriculum.steps?.map((step: any, index: number) => (
                <div
                  key={step.id}
                  className={`flex items-center gap-3 p-2 rounded-lg border ${
                    index === currentStepIndex
                      ? 'bg-primary/10 border-primary'
                      : index < currentStepIndex
                      ? 'bg-green-50 border-green-200'
                      : 'bg-muted/30 border-muted'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {index < currentStepIndex ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : index === currentStepIndex ? (
                      <Circle className="h-4 w-4 text-primary fill-primary" />
                    ) : (
                      <Circle className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium ${
                      index === currentStepIndex ? 'text-primary' : 
                      index < currentStepIndex ? 'text-green-700' : 'text-muted-foreground'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {step.estimatedTime} • {step.difficulty}
                    </p>
                  </div>
                  {index === currentStepIndex && (
                    <ChevronRight className="h-4 w-4 text-primary" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            <h1 className="text-lg font-semibold">AI Learning Agent</h1>
          </div>
          {currentPhase && (
            <Badge variant="outline" className={getPhaseColor(currentPhase)}>
              {getPhaseIcon(currentPhase)}
              <span className="ml-1 capitalize">{currentPhase}</span>
            </Badge>
          )}
        </div>
        {learningSession && (
          <div className="text-sm text-muted-foreground">
            Progress: {Math.round(learningSession.progress)}%
          </div>
        )}
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 border-r bg-card p-4 hidden md:block">
          {renderCurriculumSteps()}
          
          {/* Learning Tips */}
          {messages.some(m => 'metadata' in m && m.metadata?.learningTips) && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Learning Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {messages
                    .filter(m => 'metadata' in m && m.metadata?.learningTips)
                    .flatMap(m => (m as AgentMessage).metadata?.learningTips || [])
                    .slice(0, 5)
                    .map((tip, index) => (
                      <div key={index} className="text-sm text-muted-foreground">
                        • {tip}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages Area */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              {messages.length === 0 && (
                <div className="text-center py-8">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                    <BookOpen className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Welcome to Your AI Learning Agent!</h3>
                  <p className="text-muted-foreground mb-4">
                    I'm here to guide you through your learning journey. Tell me what you'd like to learn, and I'll create a personalized curriculum for you!
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {[
                      "I want to learn about photosynthesis",
                      "Teach me about quantum physics",
                      "Explain the nitrogen cycle",
                      "Help me understand calculus"
                    ].map((example, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuggestedAction(example)}
                      >
                        {example}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {messages.map((message) => {
                const isUser = 'content' in message && !('type' in message);
                const isAgent = 'type' in message;
                
                return (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      isUser ? "justify-end" : "justify-start"
                    }`}
                  >
                    {isAgent && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback className="text-xs">AI</AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`max-w-[85%] rounded-lg p-3 ${
                        isUser
                          ? "bg-primary text-primary-foreground"
                          : isAgent && (message as AgentMessage).type === 'curriculum'
                          ? "bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800"
                          : "bg-muted"
                      }`}
                    >
                      {isAgent && (message as AgentMessage).type === 'curriculum' && (
                        <div className="mb-2">
                          <Badge variant="outline" className="text-xs">
                            📚 Curriculum Generated
                          </Badge>
                        </div>
                      )}
                      <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                      <p
                        className={`text-xs mt-1 ${
                          isUser
                            ? "text-primary-foreground/70"
                            : "text-muted-foreground"
                        }`}
                      >
                        {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </p>
                    </div>
                    {isUser && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback className="text-xs">You</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })}

              {/* Suggested Actions */}
              {suggestedActions.length > 0 && (
                <div className="space-y-2">
                  <p className="text-xs font-medium text-muted-foreground">Suggested Actions:</p>
                  <div className="flex flex-wrap gap-2">
                    {suggestedActions.map((action, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuggestedAction(action)}
                        className="text-xs"
                      >
                        {action}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex gap-3 justify-start">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="text-xs">AI</AvatarFallback>
                  </Avatar>
                  <div className="bg-muted rounded-lg p-3">
                    <p className="text-sm">AI is thinking...</p>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="border-t p-4 bg-background">
            <div className="flex gap-2 max-w-3xl mx-auto">
              <Input
                placeholder="Ask your AI learning agent..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                disabled={isLoading}
                className="flex-1"
              />
              <Button 
                onClick={handleSendMessage} 
                disabled={isLoading || !inputMessage.trim()}
                size="icon"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}