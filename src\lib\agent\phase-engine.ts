import { 
  AgentState, 
  AgentPhaseType, 
  PhaseTransition, 
  DecisionCriteria,
  AgentAction 
} from './types';

export class PhaseTransitionEngine {
  private transitionRules: Map<string, (state: AgentState, criteria: DecisionCriteria) => PhaseTransition[]> = new Map();

  constructor() {
    this.initializeTransitionRules();
  }

  private initializeTransitionRules() {
    // Planning Phase Rules
    this.transitionRules.set('planning', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      // After planning, move to assessment to understand user's current level
      transitions.push({
        fromPhase: 'planning',
        toPhase: 'assessment',
        reason: 'Learning plan created, need to assess current knowledge level',
        confidence: 0.9,
        triggers: ['plan_complete'],
        timestamp: new Date(),
      });

      return transitions;
    });

    // Assessment Phase Rules
    this.transitionRules.set('assessment', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      if (criteria.userUnderstanding > 0.7) {
        // User has good understanding, move to explanation
        transitions.push({
          fromPhase: 'assessment',
          toPhase: 'explanation',
          reason: 'User demonstrates good understanding, ready for detailed explanation',
          confidence: 0.8,
          triggers: ['high_understanding'],
          timestamp: new Date(),
        });
      } else if (criteria.userUnderstanding < 0.3) {
        // User needs more basic explanation
        transitions.push({
          fromPhase: 'assessment',
          toPhase: 'explanation',
          reason: 'User needs foundational explanation',
          confidence: 0.8,
          triggers: ['low_understanding'],
          timestamp: new Date(),
        });
      } else {
        // Moderate understanding, proceed with explanation
        transitions.push({
          fromPhase: 'assessment',
          toPhase: 'explanation',
          reason: 'User has moderate understanding, proceeding with explanation',
          confidence: 0.7,
          triggers: ['moderate_understanding'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Explanation Phase Rules
    this.transitionRules.set('explanation', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      if (criteria.questionFrequency > 0.5) {
        // User is asking many questions, move to Q&A
        transitions.push({
          fromPhase: 'explanation',
          toPhase: 'qa',
          reason: 'User has multiple questions, entering Q&A mode',
          confidence: 0.8,
          triggers: ['high_question_frequency'],
          timestamp: new Date(),
        });
      } else if (criteria.timeSpent > 15 && criteria.userEngagement > 0.6) {
        // User has spent time and is engaged, move to practice
        transitions.push({
          fromPhase: 'explanation',
          toPhase: 'practice',
          reason: 'User engaged with explanation, ready for practice',
          confidence: 0.7,
          triggers: ['sufficient_explanation_time', 'high_engagement'],
          timestamp: new Date(),
        });
      } else if (criteria.userEngagement < 0.3) {
        // User is not engaged, try adaptation
        transitions.push({
          fromPhase: 'explanation',
          toPhase: 'adaptation',
          reason: 'Low user engagement detected, adapting approach',
          confidence: 0.6,
          triggers: ['low_engagement'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Practice Phase Rules
    this.transitionRules.set('practice', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      const accuracy = state.userPerformance.correctAnswers / Math.max(state.userPerformance.totalQuestions, 1);
      
      if (accuracy > 0.8) {
        // User is performing well, provide feedback
        transitions.push({
          fromPhase: 'practice',
          toPhase: 'feedback',
          reason: 'User performing excellently in practice',
          confidence: 0.9,
          triggers: ['high_performance'],
          timestamp: new Date(),
        });
      } else if (accuracy < 0.4) {
        // User struggling, provide feedback and adaptation
        transitions.push({
          fromPhase: 'practice',
          toPhase: 'feedback',
          reason: 'User struggling with practice, needs feedback',
          confidence: 0.8,
          triggers: ['low_performance'],
          timestamp: new Date(),
        });
      } else if (criteria.timeSpent > 20) {
        // User has practiced sufficiently
        transitions.push({
          fromPhase: 'practice',
          toPhase: 'feedback',
          reason: 'Practice session completed, providing feedback',
          confidence: 0.7,
          triggers: ['practice_time_complete'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Q&A Phase Rules
    this.transitionRules.set('qa', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      if (criteria.questionFrequency < 0.2) {
        // User questions have decreased, return to previous phase
        const previousPhase = state.previousPhase || 'explanation';
        transitions.push({
          fromPhase: 'qa',
          toPhase: previousPhase,
          reason: 'User questions resolved, returning to learning flow',
          confidence: 0.7,
          triggers: ['questions_resolved'],
          timestamp: new Date(),
        });
      } else if (criteria.timeSpent > 10) {
        // Extended Q&A session, provide summary and move on
        transitions.push({
          fromPhase: 'qa',
          toPhase: 'reflection',
          reason: 'Extended Q&A session, consolidating learning',
          confidence: 0.6,
          triggers: ['extended_qa'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Feedback Phase Rules
    this.transitionRules.set('feedback', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      if (criteria.performanceTrend === 'improving') {
        // User is improving, continue with more practice or explanation
        transitions.push({
          fromPhase: 'feedback',
          toPhase: 'practice',
          reason: 'User showing improvement, continuing practice',
          confidence: 0.8,
          triggers: ['performance_improving'],
          timestamp: new Date(),
        });
      } else if (criteria.performanceTrend === 'declining') {
        // User performance declining, adapt approach
        transitions.push({
          fromPhase: 'feedback',
          toPhase: 'adaptation',
          reason: 'User performance declining, adapting approach',
          confidence: 0.9,
          triggers: ['performance_declining'],
          timestamp: new Date(),
        });
      } else {
        // Stable performance, reflect on learning
        transitions.push({
          fromPhase: 'feedback',
          toPhase: 'reflection',
          reason: 'Performance stable, reflecting on learning',
          confidence: 0.7,
          triggers: ['stable_performance'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Adaptation Phase Rules
    this.transitionRules.set('adaptation', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      // After adaptation, return to appropriate learning phase
      if (state.userPerformance.understandingLevel < 0.4) {
        transitions.push({
          fromPhase: 'adaptation',
          toPhase: 'explanation',
          reason: 'Adapted approach for better understanding',
          confidence: 0.8,
          triggers: ['adaptation_complete'],
          timestamp: new Date(),
        });
      } else {
        transitions.push({
          fromPhase: 'adaptation',
          toPhase: 'practice',
          reason: 'Adapted approach for continued practice',
          confidence: 0.7,
          triggers: ['adaptation_complete'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Reflection Phase Rules
    this.transitionRules.set('reflection', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      const totalProgress = this.calculateOverallProgress(state);
      
      if (totalProgress > 0.8) {
        // High progress, move to completion
        transitions.push({
          fromPhase: 'reflection',
          toPhase: 'completion',
          reason: 'Learning objectives mostly achieved',
          confidence: 0.9,
          triggers: ['high_progress'],
          timestamp: new Date(),
        });
      } else {
        // More learning needed, continue with explanation
        transitions.push({
          fromPhase: 'reflection',
          toPhase: 'explanation',
          reason: 'Continuing learning journey',
          confidence: 0.7,
          triggers: ['continuing_learning'],
          timestamp: new Date(),
        });
      }

      return transitions;
    });

    // Completion Phase Rules
    this.transitionRules.set('completion', (state, criteria) => {
      const transitions: PhaseTransition[] = [];
      
      // After completion, can start new learning cycle
      transitions.push({
        fromPhase: 'completion',
        toPhase: 'planning',
        reason: 'Starting new learning cycle',
        confidence: 0.8,
        triggers: ['new_learning_cycle'],
        timestamp: new Date(),
      });

      return transitions;
    });
  }

  public evaluateTransitions(state: AgentState, criteria: DecisionCriteria): PhaseTransition[] {
    const rules = this.transitionRules.get(state.currentPhase);
    if (!rules) {
      return [];
    }

    return rules(state, criteria);
  }

  public selectBestTransition(transitions: PhaseTransition[]): PhaseTransition | null {
    if (transitions.length === 0) {
      return null;
    }

    // Select transition with highest confidence
    return transitions.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );
  }

  private calculateOverallProgress(state: AgentState): number {
    // Calculate overall progress based on various metrics
    const understandingProgress = state.userPerformance.understandingLevel;
    const engagementProgress = state.userPerformance.engagementScore;
    const phaseProgress = state.phaseHistory.length / 9; // 9 total phases
    
    return (understandingProgress + engagementProgress + phaseProgress) / 3;
  }

  public shouldTransition(state: AgentState, criteria: DecisionCriteria): boolean {
    const transitions = this.evaluateTransitions(state, criteria);
    const bestTransition = this.selectBestTransition(transitions);
    
    return bestTransition !== null && bestTransition.confidence > 0.6;
  }

  public getNextPhase(state: AgentState, criteria: DecisionCriteria): AgentPhaseType | null {
    const transitions = this.evaluateTransitions(state, criteria);
    const bestTransition = this.selectBestTransition(transitions);
    
    return bestTransition?.toPhase || null;
  }
}