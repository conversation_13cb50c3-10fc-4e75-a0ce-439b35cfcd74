import { NextRequest, NextResponse } from 'next/server';
import { AutonomousLearningAgent } from '@/lib/agent/autonomous-agent';
import { db } from '@/lib/db';
import { requireAuth } from '@/lib/clerk-sync';

// Store active agents in memory (in production, use Redis or similar)
const activeAgents = new Map<string, AutonomousLearningAgent>();

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const { action, sessionId, message, topic, goals } = await request.json();

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    let agent = activeAgents.get(sessionId);

    // Initialize agent if it doesn't exist
    if (!agent) {
      if (action === 'initialize') {
        if (!topic) {
          return NextResponse.json({ error: 'Topic is required for initialization' }, { status: 400 });
        }

        // Create study session
        const studySession = await db.studySession.create({
          data: {
            title: `Learning ${topic}`,
            description: `Autonomous learning session for ${topic}`,
            userId: user.id,
            isActive: true,
          }
        });

        // Initialize agent
        agent = new AutonomousLearningAgent(studySession.id, topic, goals || []);
        activeAgents.set(sessionId, agent);

        // Get initial action
        const initialAction = await agent.initialize();

        return NextResponse.json({
          action: initialAction,
          state: agent.getState(),
          sessionId: studySession.id,
          timestamp: new Date().toISOString(),
        });
      } else {
        return NextResponse.json({ error: 'Agent not initialized. Please initialize first.' }, { status: 400 });
      }
    }

    // Handle different actions
    switch (action) {
      case 'message':
        if (!message) {
          return NextResponse.json({ error: 'Message is required' }, { status: 400 });
        }

        const messageResult = await agent.processUserMessage(message);
        
        return NextResponse.json({
          action: messageResult.action,
          phaseTransition: messageResult.phaseTransition,
          state: agent.getState(),
          timestamp: new Date().toISOString(),
        });

      case 'action_result':
        const { actionType, result } = await request.json();
        
        if (!actionType) {
          return NextResponse.json({ error: 'Action type is required' }, { status: 400 });
        }

        const actionResult = await agent.processActionResult({
          type: actionType,
          content: '',
          confidence: 0.8
        }, result);
        
        return NextResponse.json({
          action: actionResult,
          state: agent.getState(),
          timestamp: new Date().toISOString(),
        });

      case 'get_progress':
        const progressReport = await agent.getProgressReport();
        
        return NextResponse.json({
          progress: progressReport,
          state: agent.getState(),
          timestamp: new Date().toISOString(),
        });

      case 'get_state':
        return NextResponse.json({
          state: agent.getState(),
          learningPlan: agent.getLearningPlan(),
          timestamp: new Date().toISOString(),
        });

      case 'end_session':
        // Update study session
        await db.studySession.update({
          where: { id: agent.getState().sessionId },
          data: {
            isActive: false,
            endTime: new Date(),
          }
        });

        // Remove agent from active agents
        activeAgents.delete(sessionId);
        
        return NextResponse.json({
          message: 'Session ended successfully',
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Agent API error:', error);
    if (error instanceof Error && error.message === "Authentication required") {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    const agent = activeAgents.get(sessionId);
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }

    return NextResponse.json({
      state: agent.getState(),
      learningPlan: agent.getLearningPlan(),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Agent GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}