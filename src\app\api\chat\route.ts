import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { runStudyAssistant } from '@/lib/genkit/study-flows';
import { getOrCreateUser } from '@/lib/clerk-sync';

export async function POST(request: NextRequest) {
  try {
    const { message, conversationId, sessionId } = await request.json();

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Get or create conversation
    let conversation;
    if (conversationId) {
      conversation = await db.conversation.findUnique({
        where: { id: conversationId },
        include: { messages: { orderBy: { createdAt: 'asc' } } }
      });
    }

    if (!conversation) {
      // Create new conversation if it doesn't exist
      conversation = await db.conversation.create({
        data: {
          sessionId,
          userId: (await getOrCreateUser())?.id || 'anonymous',
          title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
          systemPrompt: `You are a helpful AI study assistant. Your goal is to help students learn effectively. 
          Be patient, encouraging, and adapt your teaching style to the student's needs. 
          Break down complex topics into manageable parts and provide clear explanations. 
          Use examples and analogies to make concepts easier to understand.`
        },
        include: { messages: { orderBy: { createdAt: 'asc' } } }
      });
    }

    // Save user message
    await db.message.create({
      data: {
        conversationId: conversation.id,
        role: 'user',
        content: message,
        metadata: JSON.stringify({ timestamp: new Date().toISOString() })
      }
    });

    // Prepare conversation history for Genkit
    const conversationHistory = conversation.messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Get AI response using Genkit flow
    const genkitResponse = await runStudyAssistant({
      message,
      conversationHistory,
      sessionId,
      systemPrompt: conversation.systemPrompt,
    });

    const aiResponse = genkitResponse.response;

    // Save AI response
    await db.message.create({
      data: {
        conversationId: conversation.id,
        role: 'assistant',
        content: aiResponse,
        metadata: JSON.stringify({ 
          timestamp: new Date().toISOString(),
          suggestedActions: genkitResponse.suggestedActions || [],
          studyTips: genkitResponse.studyTips || [],
        })
      }
    });

    // Update study session activity
    if (sessionId) {
      await db.studySession.update({
        where: { id: sessionId },
        data: { updatedAt: new Date() }
      });
    }

    return NextResponse.json({
      response: aiResponse,
      conversationId: conversation.id,
      suggestedActions: genkitResponse.suggestedActions || [],
      studyTips: genkitResponse.studyTips || [],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}