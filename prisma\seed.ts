import { db } from '@/lib/db';

async function main() {
  // Create default topics
  const defaultTopics = [
    {
      name: 'Mathematics',
      description: 'Study of numbers, quantities, and shapes',
      category: 'STEM',
      difficulty: 3,
    },
    {
      name: 'Physics',
      description: 'Study of matter, energy, and their interactions',
      category: 'STEM',
      difficulty: 4,
    },
    {
      name: 'Chemistry',
      description: 'Study of substances and their properties',
      category: 'STEM',
      difficulty: 3,
    },
    {
      name: 'Biology',
      description: 'Study of living organisms and life processes',
      category: 'STEM',
      difficulty: 2,
    },
    {
      name: 'Computer Science',
      description: 'Study of computation and information systems',
      category: 'STEM',
      difficulty: 4,
    },
    {
      name: 'History',
      description: 'Study of past events and civilizations',
      category: 'Humanities',
      difficulty: 2,
    },
    {
      name: 'Literature',
      description: 'Study of written works and literary analysis',
      category: 'Humanities',
      difficulty: 2,
    },
    {
      name: 'Philosophy',
      description: 'Study of fundamental questions about existence and knowledge',
      category: 'Humanities',
      difficulty: 4,
    },
    {
      name: 'Psychology',
      description: 'Study of mind and behavior',
      category: 'Social Sciences',
      difficulty: 3,
    },
    {
      name: 'Economics',
      description: 'Study of production, consumption, and transfer of wealth',
      category: 'Social Sciences',
      difficulty: 3,
    },
  ];

  console.log('Seeding default topics...');

  for (const topic of defaultTopics) {
    try {
      await db.topic.upsert({
        where: { name: topic.name },
        update: topic,
        create: topic,
      });
      console.log(`Created/updated topic: ${topic.name}`);
    } catch (error) {
      console.error(`Error creating topic ${topic.name}:`, error);
    }
  }

  console.log('Topics seeded successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await db.$disconnect();
  });